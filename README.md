# Warden AI Enhanced - Minecraft Mod

An AI-enhanced Warden mod featuring advanced pathfinding, intelligent block placement, and improved accessibility for Minecraft 1.20.1 with Forge.

## Features

### Core Enhancements
- **Advanced AI Behavior**: Sophisticated pathfinding and decision-making algorithms that improve upon vanilla Warden behavior
- **Visual Accessibility**: Removes/disables blindness effects for better visibility during encounters
- **Intelligent Block Placement**: AI can build temporary structures (bridges, platforms, stairs) to reach players at different elevations
- **Performance Optimized**: Built-in performance monitoring and automatic throttling to maintain server stability

### Key Capabilities
- **Smart Pathfinding**: Multi-algorithm pathfinding system with 3D navigation
- **Building Intelligence**: Creates optimal building patterns based on terrain analysis
- **Automatic Cleanup**: Placed blocks are automatically removed after encounters to minimize world impact
- **Configurable Behavior**: Comprehensive configuration options for server administrators
- **Performance Monitoring**: Real-time performance tracking with optimization recommendations

## Installation

### Requirements
- Minecraft 1.20.1
- Minecraft Forge 47.4.0 or later
- Java 17 or later

### Setup Process
1. Download the mod JAR file
2. Place it in your `mods` folder
3. Launch Minecraft with Forge
4. Configure the mod settings in `config/wardenai-common.toml`

### Development Setup
1. Clone this repository
2. Run `./gradlew genIntellijRuns` (IntelliJ) or `./gradlew genEclipseRuns` (Eclipse)
3. Import the project into your IDE
4. Run the `runClient` configuration to test

## Configuration

The mod provides extensive configuration options in `config/wardenai-common.toml`:

### AI Behavior Settings
- `aiBehaviorIntensity` (1-10): Controls AI intelligence level
- `aiDecisionFrequency` (0.1-2.0): How often AI makes decisions (seconds)
- `pathfindingRange` (16-64): Maximum pathfinding calculation range

### Block Placement Settings
- `enableBlockPlacement` (true/false): Enable intelligent block placement
- `maxBlocksPlaced` (5-50): Maximum blocks per structure
- `blockPlacementCooldown` (5-60): Cooldown between placements (ticks)
- `autoCleanupBlocks` (true/false): Automatic block cleanup
- `cleanupDelay` (10-300): Delay before cleanup (seconds)

### Visual Accessibility
- `disableBlindnessEffect` (true/false): Disable blindness from Enhanced Wardens
- `enhancedVisibilityIndicators` (true/false): Add visual tracking indicators

### Performance Settings
- `enablePerformanceMonitoring` (true/false): Enable performance tracking
- `maxAiCalculationsPerTick` (5-50): Limit AI calculations per tick
- `performanceThreshold` (1.0-20.0): Performance threshold (ms per tick)

## Technical Implementation

### Architecture
- **EnhancedWardenEntity**: Core entity extending Monster with advanced AI
- **EnhancedWardenBrain**: Sophisticated decision-making system
- **AdvancedPathfinder**: Multi-algorithm pathfinding with 3D navigation
- **IntelligentBlockPlacementGoal**: Smart building system
- **BlockCleanupManager**: Automatic cleanup with safety checks
- **PerformanceMonitor**: Real-time performance tracking and optimization

### AI Decision Making
The Enhanced Warden uses a multi-layered AI system:
1. **Tactical Analysis**: Evaluates terrain, player position, and threat level
2. **Decision Tree**: Selects optimal actions based on AI intensity and situation
3. **Pathfinding**: Calculates optimal routes including building requirements
4. **Execution**: Implements decisions with performance monitoring

### Building System
The intelligent block placement system:
- Analyzes terrain and elevation differences
- Calculates optimal building patterns (stairs, ramps, bridges, platforms)
- Places blocks with safety checks and collision detection
- Tracks all placed blocks for automatic cleanup
- Respects world protection and player structures

## Performance Considerations

### Optimization Features
- **Adaptive Throttling**: Automatically reduces AI intensity under load
- **Calculation Limits**: Prevents excessive AI calculations per tick
- **Caching System**: Caches pathfinding results to reduce computation
- **Performance Monitoring**: Tracks and reports performance metrics

### Recommended Settings
- **Small Servers** (1-5 players): AI Intensity 7-8, all features enabled
- **Medium Servers** (6-15 players): AI Intensity 5-6, monitor performance
- **Large Servers** (16+ players): AI Intensity 3-4, consider disabling block placement

## Compatibility

### Tested Compatibility
- Minecraft Forge 47.4.0+
- Most major mods (JEI, Waila, etc.)
- Server environments (dedicated servers)

### Known Issues
- May conflict with mods that heavily modify Warden behavior
- Performance impact scales with number of active Enhanced Wardens
- Block placement may conflict with world protection plugins

## Development

### Building from Source
```bash
git clone <repository-url>
cd warden-ai-enhanced
./gradlew build
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes with proper documentation
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Credits

- Built with Minecraft Forge
- Uses official Mojang mappings
- Inspired by vanilla Warden mechanics

## Version History

### 1.0.0
- Initial release
- Core AI enhancement features
- Block placement system
- Performance monitoring
- Comprehensive configuration options

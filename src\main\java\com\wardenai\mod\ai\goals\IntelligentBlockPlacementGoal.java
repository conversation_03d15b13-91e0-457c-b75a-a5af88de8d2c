package com.wardenai.mod.ai.goals;

import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import com.wardenai.mod.util.BlockCleanupManager;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;

import java.util.*;

/**
 * Intelligent Block Placement Goal for Enhanced Warden
 * 
 * This goal enables the Warden to intelligently place blocks to create
 * temporary structures (bridges, platforms, stairs) to reach players
 * at different elevations. The system uses pathfinding algorithms to
 * determine optimal building patterns and includes cleanup mechanisms.
 */
public class IntelligentBlockPlacementGoal extends Goal {
    
    private final EnhancedWardenEntity warden;
    private final Level level;
    
    // Building state
    private BuildingPlan currentPlan;
    private int buildingProgress = 0;
    private int buildingCooldown = 0;
    private long planCreationTime = 0;
    
    // Block types for building (prioritized)
    private static final List<BlockState> BUILDING_BLOCKS = Arrays.asList(
        Blocks.COBBLESTONE.defaultBlockState(),
        Blocks.STONE.defaultBlockState(),
        Blocks.DIRT.defaultBlockState(),
        Blocks.DEEPSLATE.defaultBlockState()
    );

    public enum BuildingType {
        BRIDGE,      // Horizontal bridge across gaps
        STAIRS,      // Staircase for elevation
        PLATFORM,    // Platform for reaching elevated areas
        RAMP         // Gradual ramp for smooth ascent
    }

    public static class BuildingPlan {
        public final BuildingType type;
        public final List<BlockPos> blockPositions;
        public final Vec3 startPosition;
        public final Vec3 targetPosition;
        public final int priority;
        public final long creationTime;
        public final double estimatedBuildTime;

        public BuildingPlan(BuildingType type, List<BlockPos> positions, Vec3 start, Vec3 target, int priority) {
            this.type = type;
            this.blockPositions = positions != null ? new ArrayList<>(positions) : new ArrayList<>();
            this.startPosition = start;
            this.targetPosition = target;
            this.priority = priority;
            this.creationTime = System.currentTimeMillis();
            this.estimatedBuildTime = this.blockPositions.size() * 2.0; // 2 seconds per block
        }
    }

    public IntelligentBlockPlacementGoal(EnhancedWardenEntity warden) {
        this.warden = warden;
        this.level = warden.level();
        this.setFlags(EnumSet.of(Goal.Flag.MOVE, Goal.Flag.LOOK));
    }

    @Override
    public boolean canUse() {
        // Debug logging
        if (WardenAIConfig.debugMode) {
            System.out.println("BlockPlacementGoal.canUse() - enableBlockPlacement=" + WardenAIConfig.enableBlockPlacement +
                             ", buildingMode=" + warden.isInBuildingMode() +
                             ", canPlaceBlocks=" + warden.canPlaceBlocks() +
                             ", hasTarget=" + (warden.getTarget() != null));
        }

        // Only use if block placement is enabled
        if (!WardenAIConfig.enableBlockPlacement) {
            if (WardenAIConfig.debugMode) {
                System.out.println("BlockPlacementGoal.canUse() - Block placement disabled in config");
            }
            return false;
        }

        // Check if we have a target
        LivingEntity target = warden.getTarget();
        if (target == null) {
            if (WardenAIConfig.debugMode) {
                System.out.println("BlockPlacementGoal.canUse() - No target");
            }
            return false;
        }

        // Check if we can place blocks
        if (!warden.canPlaceBlocks()) {
            if (WardenAIConfig.debugMode) {
                System.out.println("BlockPlacementGoal.canUse() - Cannot place blocks (cooldown or limit)");
            }
            return false;
        }

        // Simplified check: if target is higher than us, we should build
        double heightDiff = target.getY() - warden.getY();
        boolean shouldBuild = heightDiff > 2.0;

        if (WardenAIConfig.debugMode) {
            System.out.println("BlockPlacementGoal.canUse() - heightDiff=" + heightDiff + ", shouldBuild=" + shouldBuild);
        }

        return shouldBuild;
    }

    @Override
    public boolean canContinueToUse() {
        return canUse() && currentPlan != null && buildingProgress < currentPlan.blockPositions.size();
    }

    @Override
    public void start() {
        LivingEntity target = warden.getTarget();
        if (target != null) {
            this.currentPlan = createBuildingPlan(target);
            this.buildingProgress = 0;
            this.planCreationTime = level.getGameTime();
            
            if (WardenAIConfig.debugMode && currentPlan != null) {
                System.out.println("Warden starting building plan: " + currentPlan.type + 
                                 " with " + currentPlan.blockPositions.size() + " blocks");
            }
        }
    }

    @Override
    public void stop() {
        warden.setBuildingMode(false);
        this.currentPlan = null;
        this.buildingProgress = 0;
    }

    @Override
    public void tick() {
        if (currentPlan == null) {
            return;
        }

        // Update cooldown
        if (buildingCooldown > 0) {
            buildingCooldown--;
            return;
        }

        // Check if plan is still valid
        if (!isPlanValid(currentPlan)) {
            stop();
            return;
        }

        // Execute building step
        if (buildingProgress < currentPlan.blockPositions.size()) {
            BlockPos nextBlock = currentPlan.blockPositions.get(buildingProgress);

            // Check if we need to move to the current highest block first
            BlockPos highestBlock = findHighestPlacedBlock();
            if (highestBlock != null && !isWardenOnBlock(highestBlock)) {
                // Move to the highest placed block first
                warden.getNavigation().moveTo(highestBlock.getX() + 0.5, highestBlock.getY() + 1, highestBlock.getZ() + 0.5, 1.0);

                if (WardenAIConfig.debugMode) {
                    System.out.println("Enhanced Warden: Moving to highest placed block at " + highestBlock + " before placing next block");
                }
                return; // Wait until we're on the block
            }

            if (attemptBlockPlacement(nextBlock)) {
                buildingProgress++;
                warden.setBlockPlacementCooldown();
                buildingCooldown = WardenAIConfig.blockPlacementCooldown;

                // Add to placed blocks list for cleanup
                warden.getPlacedBlocks().add(nextBlock);

                // Immediately move to the newly placed block
                warden.getNavigation().moveTo(nextBlock.getX() + 0.5, nextBlock.getY() + 1, nextBlock.getZ() + 0.5, 1.0);

                if (WardenAIConfig.debugMode) {
                    System.out.println("Enhanced Warden: Placed block at " + nextBlock +
                                     " (" + buildingProgress + "/" + currentPlan.blockPositions.size() + ") and moving to it");
                }
            } else {
                // Failed to place block, try to adapt plan
                adaptPlan(nextBlock);
            }
        } else {
            // Building complete - move to the highest block for final attack
            BlockPos highestBlock = findHighestPlacedBlock();
            if (highestBlock != null) {
                warden.getNavigation().moveTo(highestBlock.getX() + 0.5, highestBlock.getY() + 1, highestBlock.getZ() + 0.5, 1.0);
            }

            warden.setBuildingMode(false);
            if (WardenAIConfig.debugMode) {
                System.out.println("Enhanced Warden: Completed building plan: " + currentPlan.type + ", moving to highest block for attack");
            }
        }
    }

    private boolean needsBuilding(LivingEntity target) {
        Vec3 wardenPos = warden.position();
        Vec3 targetPos = target.position();
        
        double heightDiff = targetPos.y - wardenPos.y;
        double horizontalDistance = Math.sqrt(Math.pow(targetPos.x - wardenPos.x, 2) + 
                                            Math.pow(targetPos.z - wardenPos.z, 2));
        
        // Need building if target is significantly elevated and we can't reach them
        if (heightDiff > 2.0 && warden.getNavigation().createPath(target, 0) == null) {
            return true;
        }
        
        // Need building if there's a gap we need to cross
        if (horizontalDistance > 3.0 && hasGapBetween(wardenPos, targetPos)) {
            return true;
        }
        
        return false;
    }

    private BuildingPlan createBuildingPlan(LivingEntity target) {
        Vec3 wardenPos = warden.position();
        Vec3 targetPos = target.position();
        
        double heightDiff = targetPos.y - wardenPos.y;
        double horizontalDistance = Math.sqrt(Math.pow(targetPos.x - wardenPos.x, 2) + 
                                            Math.pow(targetPos.z - wardenPos.z, 2));
        
        // Determine building type based on situation
        if (heightDiff > 4.0) {
            return createStairsPlan(wardenPos, targetPos);
        } else if (heightDiff > 2.0) {
            return createRampPlan(wardenPos, targetPos);
        } else if (horizontalDistance > 3.0) {
            return createBridgePlan(wardenPos, targetPos);
        } else {
            return createPlatformPlan(wardenPos, targetPos);
        }
    }

    private BuildingPlan createStairsPlan(Vec3 start, Vec3 target) {
        List<BlockPos> blocks = new ArrayList<>();
        
        Vec3 direction = target.subtract(start).normalize();
        double heightDiff = target.y - start.y;
        int steps = Math.min((int)Math.ceil(heightDiff), WardenAIConfig.maxBlocksPlaced);
        
        for (int i = 1; i <= steps; i++) {
            double progress = (double)i / steps;
            Vec3 stepPos = start.add(direction.scale(progress * start.distanceTo(target)));
            stepPos = new Vec3(stepPos.x, start.y + (heightDiff * progress), stepPos.z);
            
            BlockPos blockPos = new BlockPos((int)stepPos.x, (int)stepPos.y - 1, (int)stepPos.z);
            if (isValidBuildingPosition(blockPos)) {
                blocks.add(blockPos);
            }
        }
        
        return new BuildingPlan(BuildingType.STAIRS, blocks, start, target, 8);
    }

    private BuildingPlan createRampPlan(Vec3 start, Vec3 target) {
        List<BlockPos> blocks = new ArrayList<>();
        
        Vec3 direction = target.subtract(start).normalize();
        double distance = start.distanceTo(target);
        double heightDiff = target.y - start.y;
        
        int rampLength = Math.min((int)Math.ceil(distance), WardenAIConfig.maxBlocksPlaced);
        
        for (int i = 1; i <= rampLength; i++) {
            double progress = (double)i / rampLength;
            Vec3 rampPos = start.add(direction.scale(distance * progress));
            rampPos = new Vec3(rampPos.x, start.y + (heightDiff * progress), rampPos.z);
            
            BlockPos blockPos = new BlockPos((int)rampPos.x, (int)rampPos.y - 1, (int)rampPos.z);
            if (isValidBuildingPosition(blockPos)) {
                blocks.add(blockPos);
            }
        }
        
        return new BuildingPlan(BuildingType.RAMP, blocks, start, target, 6);
    }

    private BuildingPlan createBridgePlan(Vec3 start, Vec3 target) {
        List<BlockPos> blocks = new ArrayList<>();
        
        Vec3 direction = target.subtract(start).normalize();
        double distance = start.distanceTo(target);
        int bridgeLength = Math.min((int)Math.ceil(distance), WardenAIConfig.maxBlocksPlaced);
        
        double bridgeHeight = Math.max(start.y, target.y);
        
        for (int i = 1; i < bridgeLength; i++) {
            Vec3 bridgePos = start.add(direction.scale(i));
            bridgePos = new Vec3(bridgePos.x, bridgeHeight, bridgePos.z);
            
            BlockPos blockPos = new BlockPos((int)bridgePos.x, (int)bridgePos.y, (int)bridgePos.z);
            if (isValidBuildingPosition(blockPos)) {
                blocks.add(blockPos);
            }
        }
        
        return new BuildingPlan(BuildingType.BRIDGE, blocks, start, target, 7);
    }

    private BuildingPlan createPlatformPlan(Vec3 start, Vec3 target) {
        List<BlockPos> blocks = new ArrayList<>();
        
        // Create a small platform near the target
        BlockPos centerPos = new BlockPos((int)target.x, (int)target.y - 1, (int)target.z);
        
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                BlockPos platformPos = centerPos.offset(x, 0, z);
                if (isValidBuildingPosition(platformPos) && blocks.size() < WardenAIConfig.maxBlocksPlaced) {
                    blocks.add(platformPos);
                }
            }
        }
        
        return new BuildingPlan(BuildingType.PLATFORM, blocks, start, target, 5);
    }

    private boolean attemptBlockPlacement(BlockPos pos) {
        if (!isValidBuildingPosition(pos)) {
            return false;
        }

        // Find the best block type to place
        BlockState blockToPlace = getBestAvailableBlock(pos);
        if (blockToPlace == null) {
            return false;
        }

        // Store original state for cleanup
        BlockState originalState = level.getBlockState(pos);

        // Place the block
        level.setBlock(pos, blockToPlace, 3);

        // Register with cleanup manager
        if (level instanceof net.minecraft.server.level.ServerLevel serverLevel) {
            BlockCleanupManager.getInstance().registerBlockPlacement(
                pos, originalState, blockToPlace, warden.getUUID(),
                currentPlan != null ? currentPlan.type.toString() : "UNKNOWN"
            );
        }

        // Play placement sound
        level.playSound(null, pos, blockToPlace.getSoundType().getPlaceSound(),
                       warden.getSoundSource(), 1.0F, 1.0F);

        return true;
    }

    private boolean isValidBuildingPosition(BlockPos pos) {
        // Check if position is air and has solid ground below or is replacing a suitable block
        BlockState currentState = level.getBlockState(pos);
        BlockState belowState = level.getBlockState(pos.below());
        
        return (currentState.isAir() || currentState.canBeReplaced()) && 
               !belowState.isAir() && 
               belowState.isSolid();
    }

    private BlockState getBestAvailableBlock(BlockPos pos) {
        // For now, use cobblestone as the default building block
        // In a more advanced implementation, this could check the Warden's inventory
        // or nearby available blocks
        return Blocks.COBBLESTONE.defaultBlockState();
    }

    private boolean hasGapBetween(Vec3 start, Vec3 target) {
        Vec3 direction = target.subtract(start).normalize();
        double distance = start.distanceTo(target);
        
        for (int i = 1; i < (int)distance; i++) {
            Vec3 checkPos = start.add(direction.scale(i));
            BlockPos blockPos = new BlockPos((int)checkPos.x, (int)checkPos.y - 1, (int)checkPos.z);
            
            if (level.getBlockState(blockPos).isAir()) {
                return true; // Found a gap
            }
        }
        
        return false;
    }

    private boolean isPlanValid(BuildingPlan plan) {
        // Check if the plan is still relevant
        LivingEntity target = warden.getTarget();
        if (target == null) {
            return false;
        }
        
        // Check if target moved too far from original position
        double targetMovement = target.position().distanceTo(plan.targetPosition);
        if (targetMovement > 5.0) {
            return false;
        }
        
        // Check if plan is too old
        long planAge = level.getGameTime() - planCreationTime;
        if (planAge > 600) { // 30 seconds
            return false;
        }
        
        return true;
    }

    private void adaptPlan(BlockPos failedBlock) {
        if (currentPlan == null) {
            return;
        }
        
        // Try to find an alternative position near the failed block
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                BlockPos alternative = failedBlock.offset(x, 0, z);
                if (isValidBuildingPosition(alternative)) {
                    // Replace the failed position with the alternative
                    currentPlan.blockPositions.set(buildingProgress, alternative);
                    return;
                }
            }
        }
        
        // If no alternative found, skip this block
        buildingProgress++;
    }

    // Helper methods for block tracking and movement
    private BlockPos findHighestPlacedBlock() {
        List<BlockPos> placedBlocks = warden.getPlacedBlocks();
        if (placedBlocks.isEmpty()) {
            return null;
        }

        BlockPos highest = null;
        int maxY = Integer.MIN_VALUE;

        for (BlockPos block : placedBlocks) {
            // Check if the block still exists
            if (!level.getBlockState(block).isAir()) {
                if (block.getY() > maxY) {
                    maxY = block.getY();
                    highest = block;
                }
            }
        }

        return highest;
    }

    private boolean isWardenOnBlock(BlockPos blockPos) {
        BlockPos wardenPos = warden.blockPosition();
        // Check if warden is standing on or very close to the block
        return wardenPos.getX() == blockPos.getX() &&
               wardenPos.getZ() == blockPos.getZ() &&
               Math.abs(wardenPos.getY() - (blockPos.getY() + 1)) <= 1;
    }

    // Getters for debugging and monitoring
    public BuildingPlan getCurrentPlan() {
        return currentPlan;
    }

    public int getBuildingProgress() {
        return buildingProgress;
    }

    public boolean isBuilding() {
        return currentPlan != null && buildingProgress < currentPlan.blockPositions.size();
    }
}

1.20.1 Changelog
47.4
====
 - 47.4.0 1.20.1 RB 4
          https://forums.minecraftforge.net/topic/154387-forge-474-minecraft-1201/

47.3
====
 - 47.3.39 Fix ForgeDev's test runs not working due to dead test mod (#10483)
 - 47.3.38 Cache this.useItem before running item break logic, Fixes #10344 (#10376)
 - 47.3.37 Speed up mod annotation scanning by ~30% (#10470)
           Co-authored-by: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 - 47.3.36 Add missed license headers (#10479)
 - 47.3.35 Add '#forge:chorus_additionally_grows_on' tag for similar mechanics to '#minecraft:azalea_grows_on' but for chorus (#10456)
 - 47.3.34 Fix cancelling ProjectileImpactEvent still firing onBlockHit (#10481)
 - 47.3.33 Honor attacker shield disabling status (#10321)
 - 47.3.32 Add fast graphics render type to block model jsons (#10393)
           Make modded leaves behave like vanilla leaves by default (Fixes #10389)
 - 47.3.31 Fix invalidly symlinked worlds crashing on level select (#10439)
 - 47.3.30 Backport even more future ResourceLocation methods (#10428)
 - 47.3.29 Ensure NetworkConstants is loaded before mod construction (#10407)
 - 47.3.28 Account for problematic mixins in VillagerTrades.EmeraldsForVillagerTypeItem (#10402)
 - 47.3.27 Fix incorrect method reference in TntBlock.explode()
 - 47.3.26 Fix issues in VillagerTrades.EmeraldsForVillagerTypeItem related to custom Villager Types (#10315)
           Add VillagerType#registerBiomeType
 - 47.3.25 Add `clientSideOnly` feature to mods.toml (#10085) (backport of #9804 to 1.20.1)
           Co-authored-by: Jonathing <<EMAIL>>
 - 47.3.24 Fix non-passengers being tickable without checking canUpdate() (#10304)
 - 47.3.23 Fix finalizeSpawn's return value not being used correctly (#10301)
 - 47.3.22 Bump CoreMods to 5.2.4 (#10263)
 - 47.3.21 Allow mipmap lowering to be disabled (#10252)
 - 47.3.20 Add optional fix of use item duration, disabled by default (#10246)
 - 47.3.19 Backport some Vanilla 1.21 `ResourceLocation` methods (#10241)
           Co-authored-by: Paint_Ninja <<EMAIL>>
 - 47.3.18 Simplify memory usage display on loading screen (#10233)
           Co-authored-by: Paint_Ninja <<EMAIL>>
 - 47.3.17 Deprecate `@ObjectHolder`, add a couple of fast-paths (#10228)
           Co-authored-by: Paint_Ninja <<EMAIL>>
 - 47.3.16 Skip Vanilla classes for the `CapabilityTokenSubclass` transformer (#10221)
           Co-authored-by: Paint_Ninja <<EMAIL>>
 - 47.3.15 Skip Forge classes in the RuntimeEnumExtender transformer (#10216)
           Mod classes are still transformed as usual
           Co-authored-by: Paint_Ninja <<EMAIL>>
 - 47.3.14 Skip processing Forge classes in `RuntimeDistCleaner` (#10208)
           Co-authored-by: Paint_Ninja <<EMAIL>>
 - 47.3.13 Disable clean on TeamCity (#10258)
 - 47.3.12 Bump CoreMods to 5.2 (#10130)
           Full Changelog:
           https://gist.github.com/Jonathing/c3ad28b2a048ac839a7baba5417ee870
           The key features are:
           - ES6 language support
           - Thoroughly updated ASMAPI, with full documentation
           - Bug fixes (some optional for backwards-compatibility)
           - Partial internal code cleanup
           - Request CoreMods to not apply fix for ASMAPI.findFirstInstructionBefore by default
           - Updated ASM to 9.7.1
           - Updated Nashorn to 15.4
 - 47.3.11 Remove unneeded boat patch (backport of #10061 to 1.20.1) (#10096)
           Co-authored-by: andan42 <<EMAIL>>
 - 47.3.10 Optionally supply FMLJavaModLoadingContext as a param to mod constructors (backport of #10074 to 1.20.1) (#10100)
           Co-authored-by: RealMangoRage <<EMAIL>>
 - 47.3.9  Minor cleanup to ModListScreen and VersionChecker (backport of #9988 to 1.20.1) (#10095)
 - 47.3.8  Cleanup FML Bindings (backport of #10004 to 1.20.1) (#10094)
 - 47.3.7  Early display fixes/workarounds for buggy drivers. Backport of #9921 to 1.20.1 (#10073)
 - 47.3.6  Add a way to render tooltips from Formatted text and TooltipComponents elements (#10055)
           Backport of #10056 for 1.20.1
 - 47.3.5  Make HangingSignBlockEntity useable with custom BlockEntityTypes. #10038
 - 47.3.4  Unlock wrapped registries when firing register events. (#10035)
           Co-authored-by: LexManos <<EMAIL>>
 - 47.3.3  Choose default JarJar mod file type based on parent JAR (#10023)
           Co-authored-by: thedarkcolour <<EMAIL>>
 - 47.3.2  Fixed falling block entities not rendering as moving blocks (#10006) (#10018)
           Co-authored-by: Ven <<EMAIL>>
 - 47.3.1  Fix boat travel distance being incorrect. Closes #9997 #9999
 - 47.3.0  1.20.1 RB 3
           https://forums.minecraftforge.net/topic/139825-forge-473-minecraft-1201/

47.2
====
 - 47.2.36 Bump gradle to 8.8
 - 47.2.35 Bump gradle and java runtime version on team city
 - 47.2.34 Fix LevelSettings ignoring data configuration. Close #9938
 - 47.2.33 Fix erroneous patch in FireBlock. Closes #9996
 - 47.2.32 Fix early window crash when parsing options.txt (#9934)
           Backport of #9933
 - 47.2.31 Prevent mixins from crashing the game when there are missing mods (#9916)
           1.20.1 backport of 49.0.14
 - 47.2.30 Fix NPE when acceptableValues in defineInList() does not allow nulls, backport of #9903 (#9907)
           Co-authored-by: J-RAP <<EMAIL>>
 - 47.2.29 Fix Crowdin (#9929)
 - 47.2.28 Optimise capabilities a tad, backport of #9886 (#9911)
 - 47.2.27 Add helper method to `OnDatapackSyncEvent`, backport of #9901 (#9919)
 - 47.2.26 Add CPU usage config option to early window, hide by default, backport of #9866 (#9915)
 - 47.2.25 Fix slightly offset mods screen link positioning, backport of #9860 (#9861)
           Co-authored-by: Dennis C <<EMAIL>>
 - 47.2.24 Make common config screen registration tasks easier, backport of #9884 (#9912)
 - 47.2.23 Add Leaves method to ModelProvider.java (#9889)
 - 47.2.22 [1.20.1] Bump CoreMods and ASM (#9897)
           - CoreMods 5.1.2 -> 5.1.6
           - ASM 9.6 -> 9.7
 - 47.2.21 Bump some deps (#9880)
           - CoreMods 5.0.1 -> 5.1.2
           - ASM 9.5 -> 9.6
           - Installer 2.1 -> 2.2
           - Installer tools 1.3.0 -> 1.4.1
 - 47.2.20 Fix missing patch for Item.onInventoryTick. Closes #9812
 - 47.2.19 Make common DisplayTest registration tasks easier (#9823)
 - 47.2.18 Optimise ForgeConfigSpec and make Range public (#9824)
           Backport of #9810 to 1.20.1
 - 47.2.17 Datagen addOptionalTag/s methods that allow passing the TagKey itself instead of passing the location (#9807) (#9808)
 - 47.2.16 Update VersionSupportMatrix.java (#9805)
 - 47.2.15 Backport of Registries optimization, now uses fastutils collections to minimize boxing
 - 47.2.14 Fix patch offset
 - 47.2.13 Fix fire related mobs not taking enough freezing damage. Closes #9686
 - 47.2.12 Fix TagLoader error not printing tag name correctly. Closes #9693
 - 47.2.11 Fix LoadingErrorScreen inner headers are not centered. Closes #9687
 - 47.2.10 Rework KeyModifiers system to properly allow keybinds to be triggered when multiple modifiers are pressed.
           Fix setting keybinds whel using keyboard inputs to select the menu. Closes #9793
 - 47.2.9  Fix KeyModifiers not being properly taken into account. Closes #9806
 - 47.2.8  Don't turn off VSync when rendering from Minecraft context (#9801)
           Co-authored-by: embeddedt <<EMAIL>>
 - 47.2.7  Fix rare crash with early display window, fixes MinecraftForge#9673 (#9799)
 - 47.2.6  Fix tag loading being unordered. Closes #9774
 - 47.2.5  Fix misaligned patch in RegistryDataLoader
 - 47.2.4  Backport CrashReportAnalyser to 1.20.1 (#9757)
 - 47.2.3  Minor MDK changes (#9752)
 - 47.2.2  Improve mod description formatting in mods screen (#9769)
           Co-authored-by: Su5eD <<EMAIL>>
 - 47.2.1  [1.20.1] Improve mod loading error message for errors inside mod constructors (#9707)
 - 47.2.0  1.20.1 RB

47.1
====
 - 47.1.47 Keep order of sources in PackRepository (#9702)
           Co-authored-by: dhyces <<EMAIL>>
 - 47.1.46 Fix DelegatingPackResources searching resource path twice (#9697)
 - 47.1.45 Fix `Level` leak in debug HUD (#9699)
           Co-authored-by: malte0811 <<EMAIL>>
 - 47.1.44 Fix PlayerSpawnPhantomsEvent not being fired (#9689)
 - 47.1.43 Enhance LivingBreathEvent and LivingDrownEvent. Closes #9680
           Also remove 3.5MB of useless data from the installer.
 - 47.1.42 Partially revert LazyOptional changes, now no longer internally uses weak references.
 - 47.1.41 Make LazyOptional's internal references to invalidation listeners use WeakReference, and allow modders to unregister themselves. Closes #8805
 - 47.1.40 Revert EntityEvent.Size changes to before #9018 was called. (#9679)
           Kept newly added methods for binary compatibility but deprecated them all for removal.
           The entire pose/eye/size system needs to be reevaluated and address some of Mojang's changes.
           However this should fix any bugs that pulling that PR may of caused.
 - 47.1.39 Add a config option to restore the calculate all normals behavior in case some setups require old broken behavior. (#9670)
 - 47.1.38 Fix rounding errors on models with 45 degree normals by favoring one Direction (#9669)
           Should fix flickering issues when breaking custom models and having our vanilla solution disabled.
 - 47.1.37 Moved ForgeHooksClient.onCreativeModeTabBuildContents to ForgeHooks to fix #9662
 - 47.1.36 Fix tag removal functionality that broke during the 1.19 update. Closes #9053 and #8949
 - 47.1.35 Replace string with forge tag in vanilla recipes. Closes #9062
 - 47.1.34 Fix new brain hooks not persisting active activities.
 - 47.1.33 Fix breaking overlay flickering on campfires by using vanilla method to calculate block normals. (#9664)
 - 47.1.32 Cleanup usages of static imports and build script so that our build doesn't spam useless error like messages.
           So that it is easier to see real errors.
           Add compatibility checking to standard testing tasks.
 - 47.1.31 Added Action value to PlayerInteractEvent.LeftClickEvent to expose what action fired the event. #9175
 - 47.1.30 Fix parameter names in IForgeDimensionSpecialEffects.adjustLightmapColors to better describe what they are. (#9656)
 - 47.1.29 Re-add EntityEvent.Size methods to maintain bincompat
 - 47.1.28 Added LivingMakeBrainEvent, to allow a consistent way for modders to manipulate entity Brains. #9292
 - 47.1.27 Add LivingSwapHandItemsEvent
 - 47.1.26 Fixed FluidUtil#tryFillContainer returning invalid result when simulating #9358
 - 47.1.25 Re-add in-game mod menu (#9652)
 - 47.1.24 Fix Entity eye height for multipart entities.
 - 47.1.23 Fix conflicting keybindings not having the correct click count set. #9360
 - 47.1.22 Fix the Emissive Rendering for Experimental Light Pipeline (#9651)
 - 47.1.21 Fixed AdvancementsScreen.java.patch buttons not rendering (#9649)
 - 47.1.20 Properly Handle Fluid Updates while in a Boat #9428
 - 47.1.19 New hook in IClientBlockExtensions to handle enabling tinting on breaking particles. #9446
 - 47.1.18 Fix invalid index when ticking itemstacks in a player nventory by adding a new onInventoryTick event. Closes #9453
 - 47.1.17 Make the FireworkShapes enum extensible (Closes #9486)
 - 47.1.16 Add `EmptyEnergyStorage` (#9487)
 - 47.1.15 Support IPv6 address compression for logged IPs
 - 47.1.14 Make item name rendering and status bar rendering respect additional gui overlays being rendered by mods (#9648)
 - 47.1.13 Fix EyeHeight event being fired twice (#9647)
 - 47.1.12 Add PlayerSpawnPhantomsEvent, utilized to block or forcefully allow PhantomSpawner to spawn phantoms (#9644)
 - 47.1.11 Fix creative mode screen not correctly using CreativeModeTab::getTabsImage (#9627)
 - 47.1.10 Add option to advertise dedicated servers to LAN.
 - 47.1.9  Fix entity eye height loop.
 - 47.1.8  Particle Description Data Provider.
 - 47.1.7  Add LivingBreatheEvent and LivingDrownEvent (#9525)
 - 47.1.6  Fix entity size event not being fired, changed it to split eye height and size calculations. (#9535)
 - 47.1.5  AlterGroundEvent for modifying block placement performed by AlterGroundDecorator (#9637)
 - 47.1.4  Change ProjectileHitEvent to return a result instead of being cancelable. Closes #9642
 - 47.1.3  Replace static import with regular one to fix S2S and non-official mappings. (#9633)
 - 47.1.2  Add missing null check for TagsProvider#existingFileHelper (#9638)
 - 47.1.1  Add GuiGraphics helpers for blitNineSliced and blitRepeating that support specifying a custom texture size (#9641)
 - 47.1.0  1.20.1 Recommended Build

47.0
====
 - 47.0.50 Fix FMLOnly loading. Closes #9609
 - 47.0.49 Improve logging for server connections (#9618)
 - 47.0.48 Fix placing fluids into waterlogged blocks with FluidUtil::tryPlaceFluid. To behave more like vanilla MC-127110 (#9586)
 - 47.0.47 Expose loaded RegistryAccess through AddReloadListenerEvent (#9613)
 - 47.0.46 Fix GLM applying to entities when killed. Closes #9551
 - 47.0.45 Add unordered creative tabs after vanilla and perform a second level sorting of tabs by registry name to ensure tabs are ordered the same between game restarts (#9612)
 - 47.0.44 Fix Early Loading window FPS snafu where it could spam (#9619)
           unlimited screen updates. Probably a good way to gently toast an ancient laptop.
 - 47.0.43 Make overloads consistent for defineListAllowEmpty in ForgeConfigSpec.Builder (#9604)
 - 47.0.42 Moved GameShuttingDownEvent hook to DedicatedServer class. Fixes #9601
 - 47.0.41 Fix PitcherCropBlock not calling canSustainPlant, not allowing it to be placed on custom farmland. Close #9611
 - 47.0.40 Add null check to NetworkHooks.openScreen. Closes #9597
 - 47.0.39 Fix ShieldBlockEvent not correctly performing damaged functions when not all damage is blocked. (#9615)
 - 47.0.38 Add IP address to client disconnect message. Closes #9603
 - 47.0.37 Fix hotbar items not dimming when sleeping in bed. Closes #9616
 - 47.0.36 Bump Eventbus to address NullPointerException when classloading things on some threads. Closes #9570
 - 47.0.35 Try and be a bit nicer about handling bad [feature] definitions  (#9606)
           * Try and be a bit nicer about handling bad [feature] definitions - they're single square bracket defined and require strings for feature bound values.
           * Some more tweaks to the feature system to output useful and well formatted error messages when bounds fail. Requires SPI 7.0.1 and the language string "fml.modloading.feature.missing" has changed.
           * Use immutable lists where possible and generally tidy things a bit
 - 47.0.34 [HotFix]: Somehow not caught by CI, but there was an issue in the SpawnUtils to handle.
 - 47.0.33 Remove amount from FluidStack hashCode to fix the equal/hashCode java contract (#9602)
 - 47.0.32 Add example usage for configs to the MDK (#9596)
           Demonstrates proper usage of different config value types, validation, transformation/parsing, load/reload handling and direct field access.
 - 47.0.31 Demonstrate configureEach in run configs (#9594)
 - 47.0.30 Reduce verbosity of prepareRuns doc in MDK (#9593)
 - 47.0.29 Lazily configure MDK tasks, improve IDE support (#9592)
 - 47.0.28 Fix not dropping xp for player sourced explosions and various other cases when xp should drop (#9588)
 - 47.0.27 add item handler capability to chiseled bookshelf (#9587)
 - 47.0.26 Fix ignoring maxUpStep method overrides on entities (#9583)
 - 47.0.25 Add missing damage type tag provider modid, existing file helper constructor overload (#9581)
 - 47.0.24 Expose holder lookup registry provider as a helper in RegistriesDatapackGenerator (#9580)
 - 47.0.23 Fix gametest collection causing secondary crash in loading error state (#9568)
 - 47.0.22 Fix SpriteCoordinateExpander not working with chained methods (MC-263524) (#9564)
 - 47.0.21 Expose EntityRendererProvider.Context to AddLayers event (#9562)
 - 47.0.20 [1.20] Add events for checking spawn conditions (SpawnPlacementCheck and PositionCheck) (#9469)
 - 47.0.19 Another tweak to the early display. We start a timer process while we create the window, in case it hangs. Also add a skip version config, to try and help in cases where the driver is stupid. (#9595)
 - 47.0.18 Auto generate names for modded loot pools. Fixes #9589 (#9591)
 - 47.0.17 More tweaks to the mod loading callbacks to make loading bars work better (#9585)
 - 47.0.16 Reimplement LootPool name patches and hooks (#9573)
 - 47.0.15 Fix experimental lighting pipeline breaking vanilla's emissive rendering. Closes #9552 (#9582)
 - 47.0.14 Update early loading default configs to match standard expectations. (#9577)
           Also allow a global override env variable for darkmode always. "FML_EARLY_WINDOW_DARK"
 - 47.0.13 Add proper duplicate mod error message that includes the mod id(s) and mod files. (#9474)
 - 47.0.12 Add missing stack tooltip rendering hooks (#9533)
           Fix automatic tooltip wrapping discarding empty lines used as spacers
 - 47.0.11 Add piston push reaction overrides to Block class (#9538)
 - 47.0.10 Fix missing calls to level-sensitive block SoundType getter (#9553)
 - 47.0.9  Fix forge registries that have wrappers/support tags not being in the HolderLookup Provider that is provided to the GatherDataEvent (#9566)
 - 47.0.8  Make IItemDecorator a functional interface again. Fixes #9563 (#9574)
 - 47.0.7  Make 1.20.x the main TC branch.
 - 47.0.6  Fix custom geometry in parent models not being resolved (#9572)
 - 47.0.5  Make the scheduled thread factory setDaemon on it's threads. Should allow things to close properly if something deadly happens early on. (#9575)
 - 47.0.4  This is an early display window system for forge. (#9558)
 - 47.0.3  fix the JIJ break by bumping SJH. apologies.
 - 47.0.2  update libs (#9565)
 - 47.0.1  Update Armor Layer Implementation to Match Vanilla (#9547)
 - 47.0.0  1.20.1 Update

46.0
====
 - 46.0.14 Fix JAR compatibility checks for 1.20 (#9556)
 - 46.0.13 [1.20] Add `AFTER_LEVEL` render level stage (#9555)
 - 46.0.12 Reorder overlay layers to match vanilla (#9550)
 - 46.0.11 Re-implement missing level-sensitive block light hook in ChunkAccess (#9536)
 - 46.0.10 Fix issues in the deserialization of empty ingredients (#9537)
 - 46.0.9  Fix wrong variable passed into EnchantmentClue lookup Fixes #9543 (#9544)
 - 46.0.8  Fix incorrect depth test state in debug graph rendering Fixes #9534 (#9539)
 - 46.0.7  Fix initCapabilities patch location in ServerLevel Fixes #9526 (#9531)
 - 46.0.6  Use Spinning Effect Intensity instead of Partial Tick for Portal Overlay Fixes #9529 (#9530)
 - 46.0.5  Fix getArmorModelHook patch, Fixex #9523 (#9528)
 - 46.0.4  Fix duplicate Map writes in PlayerList patch. (#9521)
 - 46.0.3  Fix Forge Version including MC version in MDK.
 - 46.0.2  Fix patch in light propagation (#9532)
 - 46.0.1  Attempt to fix jar signing
           Gradle 8 is stupid and doesn't (easily) allow in-place tasks, so a temporary fix has been made in ForgeGradle 6
 - 46.0.0  Forge 1.20
           - Creative mode tabs are now a registry; the `BuildContents` event was renamed to `BuildCreativeModeTabContentsEvent` and moved it to its own class
           - The pack format is now 15 for both resource packs and data packs
           - `ScreenUtils` was deprecated in favor of a `GuiGraphics` extension
           - Forge and the MDK were updated to Gradle 8 and FG6
           - The Forge common config file was removed (it only contained the deprecated old fields for resource caching, which was removed in 1.19.3)
           - Registry dummy entries were removed
           - `RemappingVertexPipeline` was fixed to forward the `endVertex()` call
           - Forge tool tags were removed in favor of vanilla ones
           Co-authored-by: ChampionAsh5357 <<EMAIL>>
           Co-authored-by: coehlrich <<EMAIL>>
           Co-authored-by: Dennis C <<EMAIL>>
           Co-authored-by: Matyrobbrt <<EMAIL>>

45.1
====
 - 45.1.0 1.19.4 Recommended Build

45.0
====
 - 45.0.66 Add method to GatherDataEvent to obtain collection of all input paths. (#9499)
 - 45.0.65 Log error when Sheets is class-loaded before registration is completed (#9475)
 - 45.0.64 [1.19.x] Re-implement RenderTooltipEvent.Color (#9497)
           * Reimplement RenderTooltipEvent.Color
           * Formatting, comments, EXC
           * Deprecate instead of replacing
 - 45.0.63 Add API for registering custom world preset editors (#9436)
 - 45.0.62 Remove unneeded extra reload of datapacks on world creation screen (#9454)
 - 45.0.61 Bump ASM to 9.5
 - 45.0.60 Fix crash when running server from root directory
           Fixes #9498
 - 45.0.59 Fix root transform matrix format, allow using all four root transform formats (#9496)
 - 45.0.58 Add missing AT lines to allow registering custom game rule boolean/integer types (#9489)
 - 45.0.57 [1.19.x] Fix SaplingGrowTreeEvent#setFeature being ignored in FungusBlock (#9485)
           Co-authored-by: Brennan Ward <<EMAIL>>
 - 45.0.56 Restore AccessibilityOnboardingScreen
           Fixes #9488
 - 45.0.55 Update documentation on FinalizeSpawn (#9467)
 - 45.0.54 Fix fluids without sound event causing exception in tryFillContainer and tryEmptyContainer (#9445)
 - 45.0.53 Make FakePlayerFactory respect the given ServerLevel (#9479)
 - 45.0.52 Collect and log exceptions occurring in DeferredWorkQueue tasks (#9449)
 - 45.0.51 Fix `NamespacedWrapper#wrapAsHolder` (#9450)
 - 45.0.50 Fixes ChatScreen calling .setScreen (#9443)
           Fix test compile failures also.
 - 45.0.49 Determine the Forge version the PR was built against when running PR compat checks (#9374)
 - 45.0.48 Add buildscript test to error on deprecated members that should of been removed. (#9460)
 - 45.0.47 Remove erroneous brace patch in Inventory (#9462)
           Fixes #9459
 - 45.0.46 [1.19.4] Move root transform builder to ModelBuilder to allow use in ItemModelBuilder (#9456)
 - 45.0.45 Fix forge grindstone hooks allowing stacks of non-stackable items (#9457)
 - 45.0.44 [1.19.4] Fix FMLOnly (#9415)
 - 45.0.43 Fix ItemLayerModel erroneously adding particle texture to layer texture list (#9441)
 - 45.0.42 Temporary fix for Canceling ProjectileImpactEvents of Piercing ammo.
           Event needs to be re-worked to have finer control. #9370
 - 45.0.41 Fix dummy air blocks not being marked as air (#9440)
 - 45.0.40 Add support for splitting the login packet (#9367)
           It contains full copies of data registries and can easily surpass vanilla's limits
 - 45.0.39 Remove Attack Range and Reach Distance and add Block Reach and Entity Reach (#9361)
 - 45.0.38 Add default bucket sounds for milk (#9432)
 - 45.0.37 Deprecate Item.onUsingTick, as vanilla provides the same function in Item.onUseTick now. Closes #9342
 - 45.0.36 Fix ScreenEvent.Init.[Pre/Post] not working correctly (#9431)
 - 45.0.35 Allow FenceGateBlock to be used without a WoodType. Closes #9392
 - 45.0.34 Deprecate duplicate tool tags that vanilla added in 1.19.4
           We will maintain a seperate 'tools' tag until Mojang adds all relevent tool tags.
           Closes #9393
 - 45.0.33 Fix BlockEvent.Break not using ItemStack enchantment hooks.
 - 45.0.32 Move Block.onCatchFire to above block removal to allow usage of BlockEntity data. Closes #9400
 - 45.0.31 Fix FinalizeSpawn not blocking spawns during worldgen (#9420)
 - 45.0.30 Fixed issue with MutableHashedLinkedMap when removing multiple sequential entries in the middle of the map.
           Added Unit tests for MutableHashLinkedMap
           Added support for removing using the iterator
           Added concurrent modification detection to the iterator
           Added default constructor with basic hashing strategy.
           Closes #9426
 - 45.0.29 Loosen access for BucketItem's canBlockContainFluid (#9421)
 - 45.0.28 Update and Regenerate Datapacks (#9419)
           Add generation for pack.mcmeta
 - 45.0.27 Restore ability to change message in ClientChatEvent (#9377)
 - 45.0.26 Remove duplicate line in FoodData patch (#9424)
           The line was accidentally duplicated in the 1.19.4 update and patching
           process.
           Fixes #9422
 - 45.0.25 Rename RegisterParticleProviderEvent's register methods to describe what kind of particle providers they register (deprecating old methods to avoid breaking) and minor docs tweaks (#9388)
 - 45.0.24 Update pack versions (#9414)
 - 45.0.23 [1.19.4] Revamp and fix spawn events (#9133)
 - 45.0.22 [1.19.4] Replace blitOffset parameter with PoseStack in IItemDecorator (#9409)
           * Replace blitOffset with PoseStack in IItemDecorator
           * Circumvent breaking changes
           * Fix blitOffset type
 - 45.0.21 Fix JSON model root transforms (#9410)
 - 45.0.20 Fix tossed items not being able to be picked up by other players. Closes #9412 (#9404)
 - 45.0.19 Fix infinite BE render bounds failing frustum intersection test. Closes #9321 (#9407)
 - 45.0.18 Make ForgeSlider use the new vanilla texture (#9406)
 - 45.0.17 Add BlockSetType#register to accesstransformer.cfg (#9386)
 - 45.0.16 Add option to completely hide a crash-callable depending on a runtime value (#9372)
 - 45.0.15 Add isNewChunk to ChunkEvent.Load (#9369)
 - 45.0.14 Remove DistExecutor calls in PistonEventTest (#9348)
 - 45.0.13 Fix hardcoded precipitation in ClimateSettingsBuilder (#9402)
           This effectively caused all biomes to have precipitation, such as
           minecraft:desert.
           Fixes #9397
 - 45.0.12 Fix incorrect variable used for swimming check (#9403)
           Because of the incorrect variable, the check to stop sprinting (and stop
           swimming) never fired correctly.
           1.19.3's `flag5` variable was renamed to `flag7` in 1.19.4; however,
           this was not caught during patching because of the fuzzy patcher.
           Fixes #9399
 - 45.0.11 Fix incorrect boolean used for glint effect (#9401)
           The `flag1` variable is ultimately controlled by whether the armor slot
           being rendered is for the leggings, which explains this bug where the
           leggings always had the enchantment glint but not any other armor piece.
           Fixes #9394
 - 45.0.10 Fixed ModMismatchDisconnectedScreen displaying missing mods wrongly (#9398)
 - 45.0.9  Fix misaligned text render type patch (#9391)
 - 45.0.8  Remove thread filter from processing clientside custom payloads. Closes @9390
 - 45.0.7  Fix LivingEntity patch which caused crash while entities got hurt. Closes #9389
 - 45.0.6  Fix wrong parameters in `Screen#renderTooltipInternal` patch (#9387)
 - 45.0.5  Fix misaligned patch in LevelRenderer. Closes #9385
 - 45.0.4  Remove our fix for MC-121048 as it has been fixed by Vanilla (#9381)
 - 45.0.3  Fix advancements not loading, bug seems to be fixed by vanilla now. Closes #9384
 - 45.0.2  Fixed patch verifier for cases where patches lowered access levels. Closes #9383
 - 45.0.1  Fix crouching while sprinting stopping the player when step height is modified. Closes #9376
 - 45.0.0  Forge 1.19.4
           Properly move `ServerStatusPing` to codec
           Reimplement custom display contexts
           Co-authored-by: Matyrobbrt <<EMAIL>>
           Co-authored-by: coehlrich <<EMAIL>>

44.1
====
 - 44.1.23 Fix experimental world warning screen appearing everytime (#9375)
 - 44.1.22 Fix continuing to use items after dropping or when a shield breaks (MC-231097, MC-168573) (#9344)
 - 44.1.21 Add onStopUsing hook to IForgeItem (#9343)
 - 44.1.20 Document RegisterParticleProvidersEvent's APIs (#9346)
 - 44.1.19 Fix incorrect ListTag.getLongArray result (MC-260378) (#9351)
 - 44.1.18 Fix missing patch that left TagBuilder#replace unused (#9354)
 - 44.1.17 Add 2 new RenderLevelStageEvent.Stage for After Entities and After Block Entities (#9259)
 - 44.1.16 Cleanup StemBlock Patch (#9337)
 - 44.1.15 Cleanup ItemProperties patch (#9332)
 - 44.1.14 Make IForgeIntrinsicHolderTagAppender methods properly chainable (#9331)
 - 44.1.13 Fix in custom fluids not respecting max height correctly. (#9319)
 - 44.1.12 Fix inconsistent vaporization in BucketItem & FluidType (#9269)
 - 44.1.11 Fix reloading event firing during server shutdown and add explicit unloading event instead (#9016)
 - 44.1.10 Homogenize and/or holdersets when serializing to prevent serializing to NBT from crashing (#9048) Fixes #9043
 - 44.1.9  [1.19.x] Fix `ForgeSlider` not respecting custom height (#9237)
 - 44.1.8  Fix stepsound for blocks in the inside_step_sound_blocks tag. (#9318)
 - 44.1.7  Fix missing hanging sign material for modded wood type (#9303)
 - 44.1.6  Fire TickEvent.LevelTickEvent on ClientLevel tick (#9299)
 - 44.1.5  Add ClientChatReceivedEvent for system messages (#9284)
 - 44.1.4  PR Action update (#9274)
 - 44.1.3  fix HangingSignEditScreen crash when using custom wood types using modid (#9294)
 - 44.1.2  Bump SecureJarHandler version, to help identify invalid mods.
 - 44.1.1  [1.19.3] Hotfix missing null check in createUnbakedItemElements (#9285)
 - 44.1.0  Mark 1.19.3 Recommended Build

44.0
====
 - 44.0.49 [1.19.3] Allow Item and Elements models to specify static color, sky light, and block light values. (#9106)
 - 44.0.48 Fix StemBlock not checking canSustainPlant for the correct block, it now checks for Melons/Pumpkins instead of the stem itself. (#9270)
 - 44.0.47 Add github shared actions for automation purposes. (#9251)
 - 44.0.46 Add translate key for Forge pack.mcmeta description (#9260)
 - 44.0.45 Fix broken link for update checker docs in mdk (#9271)
 - 44.0.44 Remove duplicate updateNeighbourForOutputSignal call Fixes #9169 (#9234)
 - 44.0.43 Add helper methods to access the set of loaded sprite locations (#9223)
 - 44.0.42 Disable guiLight3d for generated item models (#9230)
 - 44.0.41 Remove resource caching (#9254)
 - 44.0.40 Add TradeWithVillagerEvent (#9244)
 - 44.0.39 Update link for Parchment "Getting Started" (#9243)
 - 44.0.38 Allows DatapackBuiltinEntriesProvider to datagen LevelStems (#9247)
 - 44.0.37 Add a method to LootContext.Builder that allows changing the queried loot table id (#9084)
 - 44.0.36 [1.19.3] Fix Datagen Tests and Providers (#9212)
 - 44.0.35 Fix concrete powder not being hydrated by singular water sources (#9236)
 - 44.0.34 [1.19.3] Fix LootTableLoadEvent not getting fired (#9239)
 - 44.0.33 Allow using custom factories in button builders (#9238)
 - 44.0.32 Fix logspam when a root resource is requested from DelegatingPackResources, fixes #9197 (#9227)
 - 44.0.31 [1.19.3] Fix `retrieveRegistryLookup` attempting to get the registry lookup from a `HolderGetter` (#9225)
 - 44.0.30 [1.19.3] Add ability to datagen forge specific values in pack.mcmeta (#9221)
           Co-authored-by: sciwhiz12 <<EMAIL>>
 - 44.0.29 Add block atlas config to register forge:white texture (#9187)
 - 44.0.28 Fix ExtendedButton not being highlighted when focused (#9144)
 - 44.0.27 Separate checkAndFix from the check* tasks. (#9213)
 - 44.0.26 Fix forge resources overriding vanilla ones (#9222)
 - 44.0.25 Fix tooltip customization not working for creative inventory (#9218)
 - 44.0.24 Fix glowing item frame entity's texture (#9126)
           Fixes #9123
 - 44.0.23 Fix datapack registries not being synced to clients (#9219)
 - 44.0.22 Fix creatives tabs rendering overlapping tabs if the selected tab isn't on the current page. (#9214)
 - 44.0.21 Fix `SidedInvWrapper` not accounting for vanilla stacking special cases in brewing stands and furnaces (#9189)
 - 44.0.20 Update to the latest JarJar. (#9217)
 - 44.0.19 Specify NetworkHooks#getEntitySpawningPacket Generic Return Type (#9220)
 - 44.0.18 Fix using a DeferredRegister on a non-forge wrapped registry. Closes #9199
 - 44.0.17 Add support for custom CreativeModeTab implementations (#9210)
 - 44.0.16 Simplify tree grower patches (#9209)
 - 44.0.15 Replace AdvancementProvider patch with Forge helper (#9188)
 - 44.0.14 Allow using `PackOutput`s in Forge-added datagen classes (#9182)
 - 44.0.13 Add simpleBlockWithItem for data gens (#9170)
 - 44.0.12 Fix running test mods (#9211)
 - 44.0.11 [1.19.3] Fix models nested in custom geometries not resolving parents (#9200)
 - 44.0.10 Fix OBJ Loader caches not being thread-safe. (#9204)
 - 44.0.9  [1.19.3] Add event before baked models are cached by the BlockModelShaper (#9190)
 - 44.0.8  Fix compatibility checker task configuration (#9202)
 - 44.0.7  Fix chat offset (#9184)
 - 44.0.6  Redesign CreativeTab collection event to be a lot more straight forward. (#9198)
 - 44.0.5  Move ICondition patch placement to before MC throws an error.
           Disable the explicitly erroring test biome modifier.
 - 44.0.4  Fix BlockStateProvider not waiting for models before finishing. (#9196) Fixes #9195:
 - 44.0.3  Fix tooltips not rendering on screens. Closes #9191
 - 44.0.2  Fix merged mod resource pack not returning all resources with the same name when asked. Closes #9194
 - 44.0.1  Fix searching using the wrong prefix for items or tags. Fixes #9176 Fixes #9179 (#9177)
 - 44.0.0  Forge 1.19.3
           Created a CreativeModeTabEvent to register creative mode tabs and populate entries per tab
           Moved datapack registries to DataPackRegistryEvent.NewRegistry event instead of tying them to ForgeRegistry
           Made it easier for mods to datagen datapack builtin entries with DatapackBuiltinEntriesProvider
           Provided access to lookupProvider for datagen
           Updated dependencies to match versions used by vanilla and update JarJar to 0.3.18
           Added a test mod for the new CreativeModeTabEvent
           Throws better error message for Forge registries in tag datagen
           Deleted ForgeRegistryTagsProvider
           Updated ClientChatReceivedEvent and ServerChatEvent for Mojang changes
           Added patches for both sign related methods in ModelLayers
           Changed RegisterShadersEvent to use ResourceProvider
           Migrated old Mojang math types to JOML
           Co-authored-by: Marc Hermans <<EMAIL>>
           Co-authored-by: LexManos <<EMAIL>>
           Co-authored-by: sciwhiz12 <<EMAIL>>
           Co-authored-by: coehlrich <<EMAIL>>

43.2
====
 - 43.2.0 43.2 Recommended Build.

43.1
====
 - 43.1.65 Allow discovering services from the mods folder that use java's modular definition. (#9143)
 - 43.1.64 Make Datapack Registries support ICondition(s) (#9113)
 - 43.1.63 Enable additional build types to handle pull request validation. (#9159)
 - 43.1.62 Check source permission level before selector permission (#9147)
           In some situations, such as execution of a function by an advancement as
           part of its reward, a command source stack may have a backing source of
           a ServerPlayer which may lack the entity selector permission and have an
           explicit permission level that should allow the use of entity selectors,
           through CommandSourceStack#withPermission.
           We now check if the permission level of the command source stack is
           sufficient for entity selectors _before_ checking if the source is a
           player and if they have the requisite permission.
           This means that an operator permission level of 2 will always override
           the Forge entity selector permission.
           Fixes #9137
 - 43.1.61 Fix fires spreading too/igniting custom portal frames. (#9142)
 - 43.1.60 Add supplier to FlowerBlock so it works with custom MobEffects (#9139)
 - 43.1.59 Fix some logical bugs related to the Grindstone Event (#9089)
 - 43.1.58 Call baked model's `getModelData` before `getRenderTypes` (#9163)
 - 43.1.57 Make Util.memoize thread-safe (#9155)
 - 43.1.56 Rendering tweaks and fixes: Part 4 (#9065)
 - 43.1.55 Fix `Transformation` loading `PoseStack` (#9083)
 - 43.1.54 Add simple block appearance API (#9066)
 - 43.1.53 Fix invalidated modded packets when on LAN (#9157)
 - 43.1.52 Improve extensibility of DetectorRailBlock and PoweredRailBlock (#9130)
 - 43.1.51 Fix launch handler minecraft classpath locator (#9120)
 - 43.1.50 Add HitResult to `EntityTeleportEvent$EnderPearl` (#9135)
 - 43.1.49 Throw aggregate exception for erroneous registry event dispatch (#9111)
           This means that exceptions occurring during the dispatch of the registry
           events, such as those from the suppliers of RegistryObjects, properly
           cause a crash rather than merely being logged and allowing the game to
           reach the main menu.
           Fixes #8720
 - 43.1.48 Add missing semi-colon near the Dist import statement in example mod.
 - 43.1.47 Fix ClientModEvents example not subscribing to client-sided events (#9097)
 - 43.1.46 Use GitHub action to lock issues with the `spam` label (#9087)
 - 43.1.45 Remove structures slave map to Feature registry (#9091)
 - 43.1.44 Improve logging of missing or unsupported dependencies (#9104)
 - 43.1.43 [1.19.x] Fix ValueSpec caching the return value incorrectly (#9046)
 - 43.1.42 [1.19.x] Add event for registering spawn placements, and modifying existing (#9024)
 - 43.1.41 [1.19.x] Add event for items being stacked or swapped in a GUI. (#9050)
 - 43.1.40 [1.19.x] Fix PlayerInteractEvent.EntityInteractSpecific not cancelling on a server (#9079)
 - 43.1.39 Fix canceling phantom spawns preventing any further attempts that tick. (#9041)
 - 43.1.38 Rename fluid type milk translation keys (#9077)
 - 43.1.37 Fix minecart speed with water (#9076)
 - 43.1.36 Add a cancellable event that gets fired when a Totem of Undying is used (#9069)
 - 43.1.35 Fix performance issue and logging when resource caching is enabled (#9029)
 - 43.1.34 Fix NPE when feeding wolves and cats (#9074)
 - 43.1.33 Fix logically breaking change to ForgeConfigSpec.Builder#comment where modders could not add a empty line to the start of comments. (#9061)
 - 43.1.32 Fix ServiceLoader bug
 - 43.1.31 Fix ClientChatReceivedEvent for system messages
 - 43.1.30 Make ForgeConfigSpec$Builder.comment able to be called multiple times for the same entry. (#9056)
 - 43.1.29 Fix control modifier for mac with `KeyMapping`s  using Alt instead of Super (#9057)
 - 43.1.28 Fix is_desert tag not being applied correctly. (#9051)
 - 43.1.27 Fix mob griefing event for SmallFireballs not using owner entity. (#9038)
 - 43.1.26 Fix minecarts on rails not properly slowing down in water (#9033)
 - 43.1.25 Change codestyle for BookShelves tag. Closes #9027
           Add IS_CAVE tag Closes #8885
           Add IS_DESERT tag Closes #8979
           Simplify Mangrove Swamp tags Closes #8980
 - 43.1.24 Allow faces of an "elements" model to have disabled ambient occlusion (#9019)
 - 43.1.23 [1.19.x] Recipe ID-based grouping between modded and vanilla recipes. (#8876)
 - 43.1.22 Update fence_gates/wooden (#8936)
 - 43.1.21 [1.19.x] Added event for growing fungus (#8981)
 - 43.1.20 Added Bookshelves block tag (#8991)
 - 43.1.19 Create a Forge EntityType Tag for Bosses (#9017)
 - 43.1.18 Allow mods to specify shader import namespace (#9021)
 - 43.1.17 Grindstone Events (#8934)
           One to modify the output, and one to modify the input.
 - 43.1.16 Fix the serialized names of the enum (#9014)
 - 43.1.15 Fix `tryEmptyContainerAndStow` duping fluids with stackable containers (#9004)
 - 43.1.14 Add mod mismatch event (#8989)
 - 43.1.13 [1.19.x] add methods with more context to tree growers (#8956)
 - 43.1.12 [1.19.X] Adding more precise events for Advancements (#8360)
 - 43.1.11 Default IItemHandler capability for shulker box itemstacks (#8827)
           Co-authored-by: LexManos <<EMAIL>>
 - 43.1.10 [1.19] Add hook for items to remain in the hotbar when picking blocks/entities (#8872)
 - 43.1.9  [1.19.x] Block Model Builder Root Transform Support (#8860)
           Co-authored-by: sciwhiz12 <<EMAIL>>
 - 43.1.8  [1.19.x] Make LivingSetAttackTargetEvent compatible with the Brain/Behavior system. (Port of PR #8918) (#8954)
 - 43.1.7  [1.19.x] Add IForgeBlock#onTreeGrow to replace IForgeBlock#onPlantGrow from 1.16 (#8999)
 - 43.1.6  [1.19.x] Moved Player.resetAttackStrengthTicker() to the end of Player.attack() (#9000)
 - 43.1.5  fix misplaced patch in sapling block (#9005)
 - 43.1.4  Fix failed entity interactions consuming the click. (#9007)
 - 43.1.3  Fix entity selector permission check to check original source (#8995)
           Permission checks should be against the command source and not the
           target entity, as is done in vanilla.
           Fixes #8994
 - 43.1.2  Hotfix for 1.19.2 item animation bug (#8987)
           * [HOT FIX]: Fixes #8985 by no-oping for vanilla models instead of throwing error
 - 43.1.1  Add ability to Auto register capabilities via annotation (#8972)
 - 43.1.0  1.19.2 RB

43.0
====
 - 43.0.22 Added ItemDecorator API (#8794)
 - 43.0.21 [1.19.x] Custom usage animations for items (#8932)
 - 43.0.20 Allow registering custom `ColorResolver`s (#8880)
 - 43.0.19 [1.19] Allow custom outline rendering on EntityRenderers and BlockEntityRenderers (#8938)
 - 43.0.18 Redirect checks for entity selector use to a permission (#8947)
           This allows greater flexibility for configuring servers with
           operator-like permissions to user groups through the permissions API and
           their permissions handler of choice without needing to grant the
           vanilla operator permission to any player.
           The new permission is "forge:use_entity_selectors", which is granted by
           default to players with permission level 2 (GAMEMASTERS) and above.
           The hook falls back to checking the permission level if the source of
           the command is not a ServerPlayer, such as for command blocks and
           functions.
 - 43.0.17 Allow FakePlayer to report its position (#8963)
 - 43.0.16 Add alternate version of renderEntityInInventory to allow for directly specifying the angles (#8961)
 - 43.0.15 Add cancellable ToastAddEvent (#8952)
 - 43.0.14 Modify ScreenEvent.RenderInventoryMobEffects to allow moving the effect stack left or right (#8951)
 - 43.0.13 Fix Enchantment#doPostHurt and Enchantment#doPostAttack being called twice for players. Fixes MC-248272 (#8948)
 - 43.0.12 Remove reflective implementation of ICustomPacket. (#8973)
           Make vanilla custom packets able to be sent multiple times. Closes #8969
 - 43.0.11 Filter name spaces to directories only. Closes #8413
 - 43.0.10 Fix a corner case where the UMLB can not extract a version from a library. (#8967)
 - 43.0.9  Fix worlds with removed dimension types unable to load. (#8959) Closes #8800
 - 43.0.8  Fix issue where unknown chunk generators would cause DFU to fail. (#8957)
 - 43.0.7  Fix comments and documentation that were missed during the review of #8712 (#8945)
 - 43.0.6  Make AnvilUpdateEvent fire even if the second input is empty, which means it fires even if only changing the item name. (#8905)
 - 43.0.5  Fix `LivingEntity#isBlocking` to use `ToolActions#SHIELD_BLOCK` instead of `UseAnim#BLOCK` (#8933)
 - 43.0.4  Add Custom HolderSet Types allowing for logical combining of sets. (#8928)
 - 43.0.3  Add values to VersionSupportMatrix to support loading mods that restrict versions to 1.19.1 on 1.19.2 (#8946)
 - 43.0.2  Fix certain particles not updating their bounding box when their position changes (#8925)
 - 43.0.1  Update EventBus to address concurrency issue in ModLauncher Factory. Closes #8924
 - 43.0.0  1.19.2

42.0
====
 - 42.0.9 Remove calls to getStepHeight in Player#maybeBackOffFromEdge (#8927)
 - 42.0.8 Add forge tags for tools and armors, these DO NOT replace ToolActions, and are designed just for Recipes. (#8914)
 - 42.0.7 Add Biomes.BEACH to Tags (#8892)
 - 42.0.6 Let NetworkInstance.isRemotePresent check minecraft:register for channel IDs.  (#8921)
 - 42.0.5 Add an event for when the chunk ticket level is updated (#8909)
 - 42.0.4 Re-add PotentialSpawns event (#8712)
 - 42.0.3 Fix misplaced patch in ItemEntityRenderer breaking ItemEntityRenderer#shouldBob() (#8919)
 - 42.0.2 [1.19] [HotFix] Fix the dedicated server not having access to the JiJ filesystems. (#8931)
 - 42.0.1 Match Mojang's action bar fix for MC-72687 (#8917)
 - 42.0.0 Forge 1.19.1
          Load natives from classpath
          Make command argument types a forge registry
          Add `EntityMobGriefingEvent` to `Allay#wantsToPickUp`
          Overhaul `ServerChatEvent` to use `ChatDecorator` system
          Remove `ClientChatEvent#setMessage` for now
          Gradle 7.5

41.1
====
 - 41.1.0 Mark 1.19 RB

41.0
====
 - 41.0.113 Allow faces of an "elements" model to be made emissive (#8890)
 - 41.0.112 Fix invalid channel names sent from the server causing the network thread to error. (#8902)
 - 41.0.111 Fix PlayerEvent.BreakSpeed using magic block position to signify invalid position. Closes #8906
 - 41.0.110 Fix cases where URIs would not work properly with JarInJar (#8900)
 - 41.0.109 Add new hook to allow modification of lightmap via Dimension special effects (#8863)
 - 41.0.108 Fix Forge's packet handling on play messages. (#8875)
 - 41.0.107 Add API for tab list header/footer (#8803)
 - 41.0.106 Allow modded blocks overriding canStickTo prevent sticking to vanilla blocks/other modded blocks (#8837)
 - 41.0.105 Multiple tweaks and fixes to the recent changes in the client refactor PR: Part 3 (#8864)
            Fix weighted baked models not respecting children render types
            Allow fluid container model to use base texture as particle
            Fix inverted behavior in composite model building. Fixes #8871
 - 41.0.104 Fix crossbows not firing ArrowLooseEvent (#8887)
 - 41.0.103 Add User-Agent header to requests made by the update checker (#8881)
            Format: Java-http-client/<Java version> MinecraftForge/<ForgeVer> <ModId>/<ModVersion>
 - 41.0.102 Output the full path in a crash report so it is easier to find the outer mod when a crash in Jar-In-Jar occurs. (#8856)
 - 41.0.101 Clean up the pick item ("middle mouse click") patches (#8870)
 - 41.0.100 [1.19.x] Hotfix for test mods while the refactor is ongoing
 - 41.0.99  add event to SugarCaneBlock (#8877)
 - 41.0.98  Fix Global Loot Modifiers not using Dispatch Codec (#8859)
 - 41.0.97  Allow block render types to be set in datagen (#8852)
 - 41.0.96  Fix renderBreakingTexture not using the target's model data (#8849)
 - 41.0.95  Multiple tweaks and fixes to the recent changes in the client refactor PR: Part 2 (#8854)
            * Add getter for the component names in an unbaked geometry
            * Fix render type hint not being copied in BlockGeometryBakingContext
            * Ensure BlockRenderDispatches's renderSingleBlock uses the correct buffer
 - 41.0.94  [1.19.x] Apply general renames, A SRG is provided for modders. (#8840)
            See https://gist.github.com/SizableShrimp/882a671ff74256d150776da08c89ef72
 - 41.0.93  Fix mob block breaking AI not working correctly when chunk 0,0 is unloaded. Closes #8853
 - 41.0.92  Fix crash when breaking blocks with multipart models and remove caching. Closes #8850
 - 41.0.91  Fixed `CompositeModel.Baked.Builder.build()` passing arguments in the wrong order (#8846)
 - 41.0.90  Make cutout mipmaps explicitly opt-in for item/entity rendering (#8845)
            * Make cutout mipmaps explicitly opt-in for item/entity rendering
            * Default render type domain to "minecraft" in model datagens
 - 41.0.89  Fixed multipart block models not using the new model driven render type system. (#8844)
 - 41.0.88  Update to the latest JarJar to fix a collision issue where multiple jars could provide an exact match. (#8847)
 - 41.0.87  Add FML config to disable DFU optimizations client-side. (#8842)
            * Add client-side command line argument to disable DFU optimizations.
            * Switch to using FMLConfig value instead.
 - 41.0.86  [1.19] Fixed broken BufferBuilder.putBulkData(ByteBuffer) added by Forge (#8819)
            * Fixes BufferBuilder.putBulkData(ByteBuffer)
            * use nextElementByte
            * Fixed merge conflict
 - 41.0.85  [1.19.x] Fix shulker boxes allowing input of items, that return false for Item#canFitInsideContainerItems, through hoppers. (#8823)
            * Make ShulkerBoxBlockEntity#canPlaceItemThroughFace delegate to Item#canFitInsideContainerItems.
            * Switch to using Or and add comment.
            * Switch Or to And.
 - 41.0.84  [1.19.x] Added RenderLevelStageEvent to replace RenderLevelLastEvent (#8820)
            * Ported RenderLevelStageEvent from 1.18.2
            * Updated to fix merge conflicts
 - 41.0.83  [1.19.x] Fix door datagenerator (#8821)
            * Fix door datagenerator
            Fix datagenerator for door blocks. Successor to #8687, addresses comments made there about statement complexity.
            * Fix extra space around parameter
            Fix extra space before comma around a parameter.
 - 41.0.82  Create PieceBeardifierModifier to re-enable piecewise beardifier definitions (#8798)
 - 41.0.81  Allow blocks to provide a dynamic MaterialColor for display on maps (#8812)
 - 41.0.80  [1.19.x] BiomeTags Fixes/Improvements (#8711)
            * dimension specific tag fix
            * remove forge:is_beach cause vanilla has it already
            * remove forge tags for new 1.19 vanilla tags (savanna, beach, overworld, end)
            Co-authored-by: Flemmli97 <<EMAIL>>
 - 41.0.79  1.19 - Remove GlobalLootModifierSerializer and move to Codecs (#8721)
            * convert GLM serializer class to codec
            * cleanup
            * GLM list needs to be sorted
            * datagen
            * simplify serialization
            * fix test mods (oops)
            * properly use suppliers for codec as they are registry obj
 - 41.0.78  Implement item hooks for potions and enchantments (#8718)
            * Implement item hooks for potions and enchantments
            * code style fixes
 - 41.0.77  Re-apply missing patch to ServerLevel.EntityCallbacks#onTrackingEnd() (#8828)
 - 41.0.76  Double Bar Rendering fixed (#8806) (#8807)
            * Double Bar Rendering fixed (#8806)
            * Added requested changes by sciwhiz12
 - 41.0.75  Multiple tweaks and fixes to the recent changes in the client refactor PR (#8836)
            * Add an easy way to get the NamedGuiOverlay from a vanilla overlay
            * Fix static member ordering crash in UnitTextureAtlasSprite
            * Allow boss bar rendering to be cancelled
            * Make fluid container datagen use the new name
 - 41.0.74  Add FogMode to ViewportEvent.RenderFog (#8825)
 - 41.0.73  Provide additional context to the getFieldOfView event (#8830)
 - 41.0.72  Pass renderType to IForgeBakedModel.useAmbientOcclusion (#8834)
 - 41.0.71  Load custom ITransformationServices from the classpath in dev (#8818)
            * Add a classpath transformer discoverer to load custom transformation services from the classpath
            * Update ClasspathTransformerDiscoverer to 1.18
            * Update license year
            * Update license header
            * Fix the other license headers
            * Update ClasspathTransformerDiscoverer to 1.19
 - 41.0.70  Handle modded packets on the network thread (#8703)
            * Handle modded packets on the network thread
             - On the server we simply need to remove the call to
               ensureRunningOnSameThread.
             - On the client side, we now handle the packet at the very start of the
               call. We make sure we're running from a network thread to prevent
               calling the handling code twice.
               While this does mean we no longer call .release(), in practice this
               doesn't cause any leaks as ClientboundCustomPayloadPacket releases
               for us.
            * Clarify behaviour a little in the documentation
            * Javadoc formatting
            * Add a helper method for handling packets on the main thread
            Also rename the network thread one. Should make it clearer the expected
            behaviour of the two, and make it clearer there's a potentially breaking
            change.
            * Add back consumer() methods
            Also document EventNetworkChannel, to clarify the thread behaviour
            there.
            * Add since = "1.19" to deprecated annotations
 - 41.0.69  Cache resource listing calls in resource packs (#8829)
            * Make the resource lookups cached.
            * Include configurability and handle patch cleanup.
            * Document and comment the cache manager.
            * Make thread selection configurable.
            * Implement a configurable loading mechanic that falls back to default behaviour when the config is not bound yet.
            * Use boolean supplier and fix wildcard import.
            * Clean up the VPR since this is more elegant.
            * Clean up the VPR since this is more elegant.
            * Address review comments.
            * Address more review comments.
            * Fix formatting on `getSource`
            * Address comments by ichtt
            * Adapt to pups requests.
            * Stupid idea.
            * Attempt this again with a copy on write list.
            * Fix a concurrency and loading issue.
            * Fix #8813
            Checks if the paths are valid resource paths.
            * Move the new methods on vanilla Patch.
 - 41.0.68  Update SJH and JIJ
 - 41.0.67  Fix #8833 (#8835)
 - 41.0.66  Fix backwards fabulous check in SimpleBakedModel (#8832)
            Yet another blunder we missed during the review of #8786.
 - 41.0.65  Make texture atlas in StandaloneGeometryBakingContext configurable (#8831)
 - 41.0.64  [1.19.X] Client code cleanup, updates, and other refactors (#8786)
            * Revert "Allow safely registering RenderType predicates at any time (#8685)"
            This reverts commit be7275443fd939db9c58bcad47079c3767789ac1.
            * Renderable API refactors
            - Rename "render values" to "context"
            - Rename SimpleRenderable to CompositeRenderable to better reflect its use
            - Remove IMultipartRenderValues since it doesn't have any real use
            - Add extensive customization options to BakedModelRenderable
            * ClientRegistry and MinecraftForgeClient refactors
            - Add sprite loader manager and registration event
            - Add spectator shader manager and registration event
            - Add client tooltip factory manager and registration event
            - Add recipe book manager and registration event
            - Add key mapping registration event
            - Remove ClientRegistry, as everything has been moved out of it
            - Remove registration methods from MinecraftForgeClient, as they have dedicated events now
            * Dimension special effects refactors
            - Fold handlers into an extension class and remove public mutable fields
            - Add dimension special effects manager and registration event
            * HUD overlay refactors
            - Rename to IGuiOverlay match vanilla (instead of Ingame)
            - Add overlay manager and registration event
            - Move vanilla overlays to a standalone enum
            * Model loader refactors
            - Rename IModelLoader to IGeometryLoader
            - Add loader manager and registration event
            - Fold all model events into one
            - Move registration of additionally loaded models to an event
            - Remove ForgeModelBakery and related classes as they served no purpose anymore
            * Render properties refactors
            - Rename all render properties to client extensions and relocate accordingly
            - Move lookups to the respective interfaces
            * Model data refactors
            - Convert model data to a final class backed by an immutable map and document mutability requirements. This addresses several thread-safety issues in the current implementation which could result in race conditions
            - Transfer ownership of the data manager to the client level. This addresses several issues that arise when multiple levels are used at once
            * GUI and widget refactors
            - Move all widgets to the correct package
            - Rename GuiUtils and children to match vanilla naming
            * New vertex pipeline API
            - Move to vanilla's VertexConsumer
            - Roll back recent PR making VertexConsumer format-aware. This is the opposite of what vanilla does, and should not be relevant with the updated lighting pipeline
            * Lighting pipeline refactors
            - Move to dedicated lighting package
            - Separate flat and smooth lighters
            - Convert from a vertex pipeline transformer to a pure vertex source (input is baked quads)
            * Model geometry API refactors
            - Rename IModelGeometry to IUnbakedGeometry
            - Rename IModelConfiguration to IGeometryBakingContext
            - Rename other elements to match vanilla naming
            - Remove current changes to ModelState, as they do not belong there. Transforms should be specified through vanilla's system. ModelState is intended to transfer state from the blockstate JSON
            - Remove multipart geometries and geometry parts. After some discussion, these should not be exposed. Instead, geometries should be baked with only the necessary parts enabled
            * Make render types a first-class citizen in baked models
            - Add named render types (block + entity + fabulous entity)
            - Add named render type manager + registration event
            - Make BakedModel aware of render types and transfer control over which ones are used to it instead of ItemBlockRenderTypes (fallback)
            - (additional) Add concatenated list view. A wrapper for multiple lists that iterates through them in order without the cost of merging them. Useful for merging lists of baked quads
            * General event refactors
            - Several renames to either match vanilla or improve clarity
            - Relocate client chat event dispatching out of common code
            * Forge model type refactors
            - Rename SeparatePerspectiveModel to SeparateTransformsModel
            - Rename ItemModelMesherForge to ForgeItemModelShaper
            - Rename DynamicBucketModel to DynamicFluidContainerModel
            - Prefix all OBJ-related classes with "Obj" and decouple parsing from construction
            - Extract ElementsModel from model loader registry
            - Add EmptyModel (baked, unbaked and loader)
            - Refactor CompositeModel to take over ItemMultiLayerBakedModel
            - Remove FluidModel as it's not used and isn't compatible with the new fluid rendering in modern versions
            - Move model loader registration to a proper event handler
            - Update names of several JSON fields (backwards-compatible)
            - Update datagens to match
            * Miscellaneous changes and overlapping patches
            - Dispatch all new registration events
            - Convert ExtendedServerListData to a record
            - Add/remove hooks from ForgeHooksClient as necessary
            * Update test mods
            * Fix VertexConsumerWrapper returning parent instead of itself
            * Additional event cleanup pass
            As discussed on Discord:
            - Remove "@hidden" and "@see <callsite>" javadoc annotations from all client events and replace them with @ApiStatus.Internal annotation
            - Make all events that shouldn't be fired directly into abstract classes with protected constructors
            - Another styling pass, just in case (caught some missed classes)
            * Add proper deprecation javadocs and de-dupe some vertex consumer code
            * Replace sets of chunk render types with a faster BitSet-backed collection
            This largely addresses potential performance concerns that using a plain HashSet might involve by making lookups and iteration as linear as they can likely be (aside from using a plain byte/int/long for bit storage). Further performance concerns related to the implementation may be addressed separately, as all the implementation details are hidden from the end user
            * Requested changes
            - Remove MinecraftForgeClient and move members to Minecraft, IForgeMinecraft and StencilManager
            - Allow non-default elements to be passed into VertexConsumer and add support to derived classes
            - Move array instantiation out of quad processing in lighting pipeline
            - Fix flipped fluid container model
            - Set default UV1 to the correct values in the remapping pipeline
            - Minor documentation changes
            * Add/update EXC entries and fix AT comment
            * Add test mod as per Orion's request
            * Additional requested changes
            * Allow custom model types to request the particle texture to be loaded
            * Even more requested changes
            * Improve generics in ConcatenatedListView and add missing fallbacks
            * Fix fluid render types being bound to the fluid and not its holder
            * Remove non-contractual nullability in ChunkRenderTypeSet and add isEmpty
            Additionally, introduce chunk render type checks in ItemBlockRenderTypes
            Co-authored-by: Dennis C <<EMAIL>>
 - 41.0.63  Implement full support for IPv6 (#8742)
 - 41.0.62  Fix certain user-configured options being overwritten incorrectly due to validators. (#8780)
 - 41.0.61  Allow safely registering RenderType predicates at any time (#8685)
 - 41.0.60  Fix crash after loading error due to fluid texture gathering and config lookup (#8802)
 - 41.0.59  Remove the configuration option for handling empty tags in ingredients. (#8799)
            Now empty tags are considered broken in all states.
 - 41.0.58  Fix MC-105317 Structure blocks do not rotate entities correctly when loading (#8792)
 - 41.0.57  Fire ChunkWatchEvents after sending packets (#8747)
 - 41.0.56  Add item handler capability to chest boats (#8787)
 - 41.0.55  Add getter for correct BiomeSpecialEffectsBuilder to BiomeInfo$Builder (#8781)
 - 41.0.54  Fix BlockToolModificationEvent missing cancelable annotation (#8778)
 - 41.0.53  Fix ticking chunk tickets from forge's chunk manager not causing chunks to fully tick (#8775)
 - 41.0.52  Fix default audio device config loading string comparison issue (#8767)
 - 41.0.51  Fix missed vanilla method overrides in ForgeRegistry (#8766)
 - 41.0.50  Add MinecraftServer reference to ServerTickEvent (#8765)
 - 41.0.49  Fix TagsProviders for datapack registries not recognizing existing files (#8761)
 - 41.0.48  Add callback after a BlockState was changed and the neighbors were updated (#8686)
 - 41.0.47  Add biome tag entries for 1.19 biomes (#8684)
 - 41.0.46  Make fishing rods use tool actions for relevant logic (#8681)
 - 41.0.45  Update BootstrapLauncher to 1.1.1 and remove the forced
            merge of text2speech since new BSL does it.
 - 41.0.44  Merge text2speech libs together so the natives are part of the jar
 - 41.0.43  Make Forge ConfigValues implement Supplier. (#8776)
 - 41.0.42  Fix merge derp in AbstractModProvider and logic derp in ModDiscoverer
 - 41.0.41  Add "send to mods in order" method to ModList and use it (#8759)
            * Add "send to mods in order" method to ModList and use it in RegistryEvents and DataGen..
            * Also preserve order in runAll
            * Do better comparator thanks @pupnewfster
            * postEvent as well.
 - 41.0.40  Update SJH to 2.0.2.. (#8774)
            * Update SJH to 2.0.3..
 - 41.0.39  Sanity check the version specified in the mod file (#8749)
            * Sanity check the version specified in the mod file to
            make sure it's compatible with JPMS standards for
            version strings.
            Closes #8748
            Requires SPI 6
 - 41.0.38  Fix SP-Devtime world loading crash due to missing server configs (#8757)
 - 41.0.37  Remove ForgeWorldPreset and related code (#8756)
            Vanilla has a working replacement.
 - 41.0.36  Change ConfigValue#get() to throw if called before config loaded  (#8236)
            This prevents silent issues where a mod gets the value of the setting
            before configs are loaded, which means the default value is always
            returned.
            As there may be situations where the getting the config setting before
            configs are loaded is needed, and it is not preferable to hardcode the
            default value, the original behavior is made available through #getRaw.
            Implements and closes #7716
            * Remove getRaw() method
            This is effectively replaced with the expression `spec.isLoaded() ?
            configValue.get() : configValue.getDefault()`.
            * Remove forceSystemNanoTime config setting
            As implemented, it never had any effect as any place where the config
            value would be queried happens before the configs are loaded.
 - 41.0.35  Fix EnumArgument to use enum names for suggestions (#8728)
            Previously, the suggestions used the string representation of the enum
            through Enum#toString, which can differ from the name of the enum as
            required by Enum#valueOf, causing invalid suggestions (both in gui and
            through the error message).
 - 41.0.34  Jar-In-Jar (#8715)
 - 41.0.33  [1.19] Fix data-gen output path of custom data-pack registries (#8724)
 - 41.0.32  Fix player dive and surface animations in custom fluids (#8738)
 - 41.0.31  [1.19.x] Affect ItemEntity Motion in Custom Fluids (#8737)
 - 41.0.30  [1.19] Add support for items to add enchantments without setting them in NBT (#8719)
 - 41.0.29  [1.19.x] Add stock biome modifier types for adding features and spawns (#8697)
 - 41.0.28  [1.19.x] Fluid API Overhaul (#8695)
 - 41.0.27  Replace StructureSpawnListGatherEvent with StructureModifiers (#8717)
 - 41.0.26  Use stack sensitive translation key by default for FluidAttributes. (#8707)
 - 41.0.25  Delete LootItemRandomChanceCondition which added looting bonus enchantment incorrectly. (#8733)
 - 41.0.24  Update EventBus to 6.0, ModLauncher to 10.0.1 and BootstrapLauncher to 1.1 (#8725)
 - 41.0.23  Replace support bot with support action (#8700)
 - 41.0.22  Fix Reach Distance / Attack Range being clamped at 6.0 (#8699)
 - 41.0.21  [1.19.x] Fix mods' worldgen data not being loaded when creating new singleplayer worlds (#8693)
 - 41.0.20  [1.19.x] Fix experimental confirmation screen (#8727)
 - 41.0.19  Move is_mountain to forge's tag instead of vanilla's (#8726)
 - 41.0.18  [1.19.x] Add CommandBuildContext to Register Command Events (#8716)
 - 41.0.17  Only rewrite datagen cache when needed (#8709)
 - 41.0.16  Implement a simple feature system for Forge (#8670)
            * Implement a simple feature system for Forge. Allows mods to demand certain features are available in the loading system. An example for java_version is provided, but not expected to be used widely. This is more targeted to properties of the display, such as GL version and glsl profile.
            Requires https://github.com/MinecraftForge/ForgeSPI/pull/13 to be merged first in ForgeSPI, and the SPI to be updated appropriately in build.gradle files.
            * rebase onto 1.19 and add in SPI update
 - 41.0.15  displayTest option in mods.toml (#8656)
            * displayTest option in mods.toml
            * "MATCH_VERSION" (or none) is existing match version string behaviour
            * "IGNORE_SERVER_VERSION" accepts anything and sends special SERVERONLY string
            * "IGNORE_ALL_VERSION" accepts anything and sends an empty string
            * "NONE" allows the mod to supply their own displaytest using the IExtensionPoint mechanism.
            * Update display test with feedback and added the mods.toml discussion in mdk.
 - 41.0.14  Update forgeSPI to v5 (#8696)
 - 41.0.13  Make IVertexConsumers such as the lighting pipeline, be aware of which format they are dealing with. (#8692)
            Also fix Lighting pipeline ignoring the overlay coords from the block renderer.
 - 41.0.12  Fixed misaligned patch to invalidateCaps in Entity (#8705)
 - 41.0.11  Fix readAdditionalLevelSaveData (#8704)
 - 41.0.10  Fixes setPos to syncPacketPositionCodec (#8702)
 - 41.0.9   Fix wrong param passed to PlayLevelSoundEvent.AtEntity (#8688)
 - 41.0.8   Override initialize in SlotItemHandler, so it uses the itemhandler instead of container (#8679)
 - 41.0.7   Update MDK for 1.19 changes (#8675)
 - 41.0.6   Add helper to RecipeType, and fix eclipse compiler error in test class.
 - 41.0.5   Update modlauncher to latest (#8691)
 - 41.0.4   Fix getting entity data serializer id crashing due to improper port to new registry system (#8678)
 - 41.0.3   Fire registry events in the order vanilla registers to registries (#8677)
            Custom registries are still fired in alphabetical order, after all vanilla registries.
            Move forge's data_serializers registry to forge namespace.
 - 41.0.2   Add method with pre/post wrap to allow setting/clearing mod context. (#8682)
            Fixes ActiveContainer in ModContext not being present in registry events. Closes #8680
 - 41.0.1   Fix the Curlie oopsie
 - 41.0.0   Forge 1.19
            * Bump pack.mcmeta formats
            * 1.19 biome modifiers
            * Mark ClientPlayerNetworkEvent.LoggedOutEvent's getters as nullable
            * Add docs and package-info to client extension interfaces package
            * Move RenderBlockOverlayEvent hooks to ForgeHooksClient
            * Add package-infos to client events package
            * Rename SoundLoadEvent to SoundEngineLoadEvent
            This reduces confusion from consumers which may think the
            name SoundLoadEvent refers to an individual sound being loaded rather
            than the sound engine.
            * Document and change SoundLoadEvent to fire on mod bus
            Previously, it fired on both the mod bus and the Forge bus, which is
            confusing for consumers.
            * Delete SoundSetupEvent
            Looking at its original implementation shows that there isn't an
            appropriate place in the new sound code to reinsert the event, and the
            place of 'sound engine/manager initialization event' is taken already by SoundLoadEvent.
            * Perform some cleanup on client events
             - Removed nullable annotations from ClientPlayerNetworkEvent
             - Renamed #getPartialTicks methods to #getPartialTick, to be consistent
              with vanilla's naming of the partial tick
             - Cleanup documentation to remove line breaks, use the
              spelling 'cancelled' over
              'canceled', and improve docs on existing and
               new methods.
            * Remove EntityEvent.CanUpdate
            Closes MinecraftForge/MinecraftForge#6394
            * Switch to Jetbrains nullability annotations
            * New PlayLevelSoundEvent; replaces old PlaySoundAtEntityEvent
            * Remove ForgeWorldPresetScreens
            * Remove IForgeRegistryEntry
            * Remove use of List<Throwable> in FML's CompletableFutures
            * Add docs to mod loading stages, stages, and phases
            * Gradle 7.4.2
            * Use SLF4J in FMLLoader and other subprojects
            * Switch dynamic versions in subprojects to pinned ones
            * Switch ForgeRoot and MDK to FG plugin markers
            * Configure Forge javadoc task
            The task now uses a custom stylesheet with MCForge elements, and
            configured to combine the generation from the four FML subprojects
            (fmlloader, fmlcore, javafmllanguage, mclanguage) and the Forge project
            into the javadoc output.
            * Update docs/md files, for 1.19 update and the move away from IRC to Discord.
            * Make "Potentially dangerous alternative prefix" a debug warning, not info.
            Co-authored-by: Curle <<EMAIL>>
            Co-authored-by: sciwhiz12 <<EMAIL>>


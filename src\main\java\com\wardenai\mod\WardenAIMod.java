package com.wardenai.mod;

import com.mojang.logging.LogUtils;
import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import com.wardenai.mod.entity.ModEntities;
import com.wardenai.mod.item.ModItems;
import net.minecraft.core.registries.Registries;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.item.CreativeModeTab;
import net.minecraft.world.item.CreativeModeTabs;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.block.Block;
import net.minecraftforge.event.entity.EntityAttributeCreationEvent;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.BuildCreativeModeTabContentsEvent;
import net.minecraftforge.event.server.ServerStartingEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.config.ModConfig;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;
import org.slf4j.Logger;

/**
 * Main mod class for Warden AI Enhanced
 * 
 * This mod enhances the vanilla Warden with advanced AI capabilities including:
 * - Sophisticated pathfinding and decision-making algorithms
 * - Intelligent block placement for reaching players at different elevations
 * - Improved accessibility by removing blindness effects
 * - Performance-optimized AI systems
 * - Comprehensive configuration options for server administrators
 */
@Mod(WardenAIMod.MODID)
public class WardenAIMod {
    
    // Define mod id in a common place for everything to reference
    public static final String MODID = "wardenai";
    
    // Directly reference a slf4j logger
    private static final Logger LOGGER = LogUtils.getLogger();
    
    // Create Deferred Registers for mod content
    public static final DeferredRegister<Block> BLOCKS = DeferredRegister.create(ForgeRegistries.BLOCKS, MODID);
    public static final DeferredRegister<Item> ITEMS = DeferredRegister.create(ForgeRegistries.ITEMS, MODID);
    public static final DeferredRegister<EntityType<?>> ENTITY_TYPES = DeferredRegister.create(ForgeRegistries.ENTITY_TYPES, MODID);
    public static final DeferredRegister<CreativeModeTab> CREATIVE_MODE_TABS = DeferredRegister.create(Registries.CREATIVE_MODE_TAB, MODID);

    // Creative tab for the mod
    public static final RegistryObject<CreativeModeTab> WARDEN_AI_TAB = CREATIVE_MODE_TABS.register("warden_ai_tab", 
        () -> CreativeModeTab.builder()
            .withTabsBefore(CreativeModeTabs.SPAWN_EGGS)
            .icon(() -> net.minecraft.world.item.Items.SCULK_SENSOR.getDefaultInstance())
            .displayItems((parameters, output) -> {
                // Add Enhanced Warden spawn egg to the creative tab
                output.accept(ModItems.ENHANCED_WARDEN_SPAWN_EGG.get());
            }).build());

    public WardenAIMod(FMLJavaModLoadingContext context) {
        IEventBus modEventBus = context.getModEventBus();

        // Register the commonSetup method for modloading
        modEventBus.addListener(this::commonSetup);

        // Register the Deferred Registers to the mod event bus
        BLOCKS.register(modEventBus);
        ITEMS.register(modEventBus);
        ENTITY_TYPES.register(modEventBus);
        CREATIVE_MODE_TABS.register(modEventBus);

        // Register ourselves for server and other game events
        MinecraftForge.EVENT_BUS.register(this);

        // Register the item to a creative tab
        modEventBus.addListener(this::addCreative);

        // Register entity attributes
        modEventBus.addListener(this::entityAttributes);

        // Register our mod's ForgeConfigSpec so that Forge can create and load the config file
        context.registerConfig(ModConfig.Type.COMMON, WardenAIConfig.SPEC);
        
        LOGGER.info("Warden AI Enhanced mod initialized");
    }

    private void commonSetup(final FMLCommonSetupEvent event) {
        // Common setup code
        LOGGER.info("Warden AI Enhanced - Common Setup");
        
        // Initialize entity registrations
        ModEntities.init();
        ModItems.init();
        
        LOGGER.info("Enhanced Warden AI systems initialized");
    }

    // Add mod items to creative tabs
    private void addCreative(BuildCreativeModeTabContentsEvent event) {
        // Add spawn eggs and other items to appropriate creative tabs
        if (event.getTabKey() == CreativeModeTabs.SPAWN_EGGS) {
            event.accept(ModItems.ENHANCED_WARDEN_SPAWN_EGG);
        }
    }

    // Register entity attributes
    private void entityAttributes(EntityAttributeCreationEvent event) {
        event.put(ModEntities.ENHANCED_WARDEN.get(), EnhancedWardenEntity.createAttributes().build());
    }

    // Server starting event
    @SubscribeEvent
    public void onServerStarting(ServerStartingEvent event) {
        LOGGER.info("Warden AI Enhanced - Server Starting");
        LOGGER.info("AI behavior intensity: {}", WardenAIConfig.aiBehaviorIntensity);
        LOGGER.info("Block placement enabled: {}", WardenAIConfig.enableBlockPlacement);
        LOGGER.info("Blindness effect disabled: {}", WardenAIConfig.disableBlindnessEffect);
    }

    // Client-side event handling
    @Mod.EventBusSubscriber(modid = MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
    public static class ClientModEvents {
        @SubscribeEvent
        public static void onClientSetup(FMLClientSetupEvent event) {
            LOGGER.info("Warden AI Enhanced - Client Setup");
            // Client-side initialization will go here
        }
    }
}

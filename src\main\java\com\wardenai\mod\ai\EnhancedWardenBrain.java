package com.wardenai.mod.ai;

import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Enhanced AI Brain for the Warden
 * 
 * Implements sophisticated decision-making algorithms that improve upon
 * the vanilla Warden's behavior patterns. The brain analyzes the environment,
 * player behavior, and tactical situations to make intelligent decisions.
 */
public class EnhancedWardenBrain {
    
    private final EnhancedWardenEntity warden;
    private final Level level;
    private final Random random;
    
    // Decision-making state
    private AIState currentState = AIState.IDLE;
    private LivingEntity currentTarget;
    private BlockPos targetPosition;
    private int decisionCooldown = 0;
    
    // Tactical analysis
    private TacticalSituation lastTacticalAnalysis;
    private long lastAnalysisTime = 0;
    
    // Behavior patterns
    private List<AIDecision> recentDecisions = new ArrayList<>();
    private int maxDecisionHistory = 10;

    public enum AIState {
        IDLE,
        HUNTING,
        PURSUING,
        BUILDING_PATH,
        ATTACKING,
        RETREATING,
        ANALYZING
    }

    public enum ThreatLevel {
        LOW,
        MEDIUM,
        HIGH,
        CRITICAL
    }

    public static class TacticalSituation {
        public final Vec3 wardenPosition;
        public final Vec3 targetPosition;
        public final double distanceToTarget;
        public final double heightDifference;
        public final boolean hasLineOfSight;
        public final boolean targetIsElevated;
        public final boolean canReachTarget;
        public final ThreatLevel threatLevel;
        public final List<BlockPos> possibleBuildingPositions;

        public TacticalSituation(Vec3 wardenPos, Vec3 targetPos, double distance, double heightDiff,
                               boolean lineOfSight, boolean elevated, boolean reachable, ThreatLevel threat,
                               List<BlockPos> buildingPositions) {
            this.wardenPosition = wardenPos;
            this.targetPosition = targetPos;
            this.distanceToTarget = distance;
            this.heightDifference = heightDiff;
            this.hasLineOfSight = lineOfSight;
            this.targetIsElevated = elevated;
            this.canReachTarget = reachable;
            this.threatLevel = threat;
            this.possibleBuildingPositions = buildingPositions != null ? buildingPositions : new ArrayList<>();
        }
    }

    public static class AIDecision {
        public final AIState newState;
        public final String reasoning;
        public final long timestamp;
        public final int priority;

        public AIDecision(AIState state, String reason, int priority) {
            this.newState = state;
            this.reasoning = reason;
            this.timestamp = System.currentTimeMillis();
            this.priority = priority;
        }
    }

    public EnhancedWardenBrain(EnhancedWardenEntity warden) {
        this.warden = warden;
        this.level = warden.level();
        this.random = new Random();
    }

    /**
     * Main brain tick - called periodically to make AI decisions
     */
    public void tick() {
        if (decisionCooldown > 0) {
            decisionCooldown--;
            return;
        }

        // Update current target
        this.currentTarget = warden.getTarget();
        
        if (currentTarget == null) {
            this.transitionToState(AIState.IDLE, "No target detected", 1);
            return;
        }

        // Analyze tactical situation
        TacticalSituation situation = analyzeTacticalSituation();
        
        // Make decision based on AI intensity and situation
        AIDecision decision = makeDecision(situation);
        
        if (decision != null) {
            executeDecision(decision);
            recordDecision(decision);
        }

        // Set cooldown based on AI intensity (higher intensity = more frequent decisions)
        int baseCooldown = 20; // 1 second
        int intensityModifier = Math.max(1, 11 - WardenAIConfig.aiBehaviorIntensity);
        this.decisionCooldown = baseCooldown / intensityModifier;
    }

    /**
     * Analyzes the current tactical situation
     */
    private TacticalSituation analyzeTacticalSituation() {
        if (currentTarget == null) {
            return null;
        }

        Vec3 wardenPos = warden.position();
        Vec3 targetPos = currentTarget.position();
        
        double distance = wardenPos.distanceTo(targetPos);
        double heightDiff = targetPos.y - wardenPos.y;
        
        boolean hasLineOfSight = warden.hasLineOfSight(currentTarget);
        boolean targetIsElevated = heightDiff > 2.0;
        boolean canReachTarget = warden.getNavigation().createPath(currentTarget, 0) != null;
        
        ThreatLevel threatLevel = assessThreatLevel(distance, heightDiff, hasLineOfSight);
        
        List<BlockPos> buildingPositions = new ArrayList<>();
        if (WardenAIConfig.enableBlockPlacement && targetIsElevated && !canReachTarget) {
            buildingPositions = findBuildingPositions(wardenPos, targetPos);
        }

        TacticalSituation situation = new TacticalSituation(
            wardenPos, targetPos, distance, heightDiff, hasLineOfSight,
            targetIsElevated, canReachTarget, threatLevel, buildingPositions
        );

        this.lastTacticalAnalysis = situation;
        this.lastAnalysisTime = level.getGameTime();
        
        return situation;
    }

    /**
     * Makes an AI decision based on the tactical situation
     */
    private AIDecision makeDecision(TacticalSituation situation) {
        if (situation == null) {
            return new AIDecision(AIState.IDLE, "No tactical situation to analyze", 1);
        }

        // Decision tree based on AI intensity and situation
        int intensity = warden.getAIIntensity();
        
        // High-priority decisions (immediate threats)
        if (situation.distanceToTarget <= 3.0 && situation.hasLineOfSight) {
            return new AIDecision(AIState.ATTACKING, "Target in melee range with line of sight", 10);
        }

        // HIGH PRIORITY: Building behavior for elevated targets
        if (situation.targetIsElevated && WardenAIConfig.enableBlockPlacement && warden.canPlaceBlocks()) {
            return new AIDecision(AIState.BUILDING_PATH, "Target elevated, prioritizing building over ranged attacks", 9);
        }

        // VERY HIGH PRIORITY: Force building mode if target is significantly elevated
        if (situation.heightDifference > 3.0 && WardenAIConfig.enableBlockPlacement) {
            if (warden.canPlaceBlocks()) {
                return new AIDecision(AIState.BUILDING_PATH, "Target elevated, must build to reach", 10);
            }
        }

        // AGGRESSIVE BUILDING: Even for moderate height differences, prefer building
        if (situation.heightDifference > 2.0 && WardenAIConfig.enableBlockPlacement && warden.canPlaceBlocks()) {
            return new AIDecision(AIState.BUILDING_PATH, "Moderate elevation, building preferred over ranged attack", 8);
        }

        // Standard pursuit - but check if we should build instead of using ranged attacks
        if (situation.hasLineOfSight && situation.canReachTarget) {
            // Even if we can reach the target, if they're elevated, prefer building for a more aggressive approach
            if (situation.heightDifference > 1.5 && WardenAIConfig.enableBlockPlacement && warden.canPlaceBlocks()) {
                return new AIDecision(AIState.BUILDING_PATH, "Target reachable but elevated, building for aggressive approach", 7);
            }
            return new AIDecision(AIState.PURSUING, "Clear path to target", 6);
        }

        // Advanced pathfinding for higher AI intensities
        if (intensity >= 5 && !situation.canReachTarget) {
            return new AIDecision(AIState.ANALYZING, "Analyzing complex pathfinding options", 4);
        }

        // Basic hunting behavior
        if (situation.distanceToTarget <= 16.0) {
            return new AIDecision(AIState.HUNTING, "Target within hunting range", 3);
        }

        return new AIDecision(AIState.IDLE, "No immediate action required", 1);
    }

    /**
     * Executes the given AI decision
     */
    private void executeDecision(AIDecision decision) {
        if (WardenAIConfig.debugMode) {
            System.out.println("Warden AI Decision: " + decision.newState + " - " + decision.reasoning);
        }

        switch (decision.newState) {
            case ATTACKING:
                executeAttackBehavior();
                break;
            case PURSUING:
                executePursuitBehavior();
                break;
            case BUILDING_PATH:
                executeBuildingBehavior();
                break;
            case HUNTING:
                executeHuntingBehavior();
                break;
            case ANALYZING:
                executeAnalysisBehavior();
                break;
            case RETREATING:
                executeRetreatBehavior();
                break;
            case IDLE:
            default:
                executeIdleBehavior();
                break;
        }

        this.currentState = decision.newState;
    }

    private void executeAttackBehavior() {
        if (currentTarget != null) {
            warden.getNavigation().moveTo(currentTarget, 1.2);
            warden.setBuildingMode(false);
        }
    }

    private void executePursuitBehavior() {
        if (currentTarget != null) {
            warden.getNavigation().moveTo(currentTarget, 1.0);
            warden.setBuildingMode(false);
        }
    }

    private void executeBuildingBehavior() {
        warden.setBuildingMode(true);
        if (WardenAIConfig.debugMode) {
            System.out.println("Enhanced Warden: Entering building mode! Target elevated, attempting to build path.");
        }
        // The IntelligentBlockPlacementGoal will handle the actual building
    }

    private void executeHuntingBehavior() {
        if (currentTarget != null) {
            warden.getNavigation().moveTo(currentTarget, 0.8);
            warden.setBuildingMode(false);
        }
    }

    private void executeAnalysisBehavior() {
        // Advanced pathfinding analysis will be handled by the AdvancedPathfinder
        warden.getAdvancedPathfinder().analyzeComplexPath(currentTarget);
        warden.setBuildingMode(false);
    }

    private void executeRetreatBehavior() {
        // Move away from target (for future use in complex scenarios)
        if (currentTarget != null) {
            Vec3 retreatDirection = warden.position().subtract(currentTarget.position()).normalize();
            Vec3 retreatTarget = warden.position().add(retreatDirection.scale(10));
            warden.getNavigation().moveTo(retreatTarget.x, retreatTarget.y, retreatTarget.z, 1.0);
        }
        warden.setBuildingMode(false);
    }

    private void executeIdleBehavior() {
        warden.setBuildingMode(false);
        // Default idle behavior
    }

    private ThreatLevel assessThreatLevel(double distance, double heightDiff, boolean hasLineOfSight) {
        if (distance <= 3.0 && hasLineOfSight) {
            return ThreatLevel.CRITICAL;
        } else if (distance <= 8.0 && hasLineOfSight) {
            return ThreatLevel.HIGH;
        } else if (distance <= 16.0) {
            return ThreatLevel.MEDIUM;
        } else {
            return ThreatLevel.LOW;
        }
    }

    private List<BlockPos> findBuildingPositions(Vec3 wardenPos, Vec3 targetPos) {
        List<BlockPos> positions = new ArrayList<>();
        
        // Simple building position calculation - can be enhanced
        Vec3 direction = targetPos.subtract(wardenPos).normalize();
        
        for (int i = 1; i <= 5; i++) {
            Vec3 buildPos = wardenPos.add(direction.scale(i));
            BlockPos blockPos = new BlockPos((int)buildPos.x, (int)buildPos.y, (int)buildPos.z);
            
            if (level.getBlockState(blockPos).isAir() && 
                !level.getBlockState(blockPos.below()).isAir()) {
                positions.add(blockPos);
            }
        }
        
        return positions;
    }

    private void transitionToState(AIState newState, String reasoning, int priority) {
        if (this.currentState != newState) {
            AIDecision decision = new AIDecision(newState, reasoning, priority);
            executeDecision(decision);
            recordDecision(decision);
        }
    }

    private void recordDecision(AIDecision decision) {
        recentDecisions.add(decision);
        if (recentDecisions.size() > maxDecisionHistory) {
            recentDecisions.remove(0);
        }
    }

    // Getters
    public AIState getCurrentState() {
        return currentState;
    }

    public TacticalSituation getLastTacticalAnalysis() {
        return lastTacticalAnalysis;
    }

    public List<AIDecision> getRecentDecisions() {
        return new ArrayList<>(recentDecisions);
    }
}

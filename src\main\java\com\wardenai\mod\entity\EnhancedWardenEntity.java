package com.wardenai.mod.entity;

import com.wardenai.mod.ai.EnhancedWardenBrain;
import com.wardenai.mod.ai.goals.IntelligentBlockPlacementGoal;
import com.wardenai.mod.ai.pathfinding.AdvancedPathfinder;
import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.util.PerformanceMonitor;
import com.wardenai.mod.util.BlockCleanupManager;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.monster.warden.Warden;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

/**
 * Enhanced Warden Entity with advanced AI capabilities
 * 
 * Features:
 * - Advanced pathfinding and decision-making algorithms
 * - Intelligent block placement for reaching players
 * - Improved accessibility (no blindness effect)
 * - Performance-optimized AI systems
 * - Configurable behavior intensity
 */
public class EnhancedWardenEntity extends Monster {
    
    // Data accessors for syncing data between client and server
    private static final EntityDataAccessor<Integer> AI_INTENSITY = 
        SynchedEntityData.defineId(EnhancedWardenEntity.class, EntityDataSerializers.INT);
    private static final EntityDataAccessor<Boolean> BUILDING_MODE = 
        SynchedEntityData.defineId(EnhancedWardenEntity.class, EntityDataSerializers.BOOLEAN);
    
    // AI and pathfinding components
    private EnhancedWardenBrain enhancedBrain;
    private AdvancedPathfinder advancedPathfinder;
    private PerformanceMonitor performanceMonitor;
    
    // Block placement tracking
    private List<BlockPos> placedBlocks = new ArrayList<>();
    private int blockPlacementCooldown = 0;
    private long lastAIDecision = 0;
    
    // Performance tracking
    private int aiCalculationsThisTick = 0;
    private long tickStartTime = 0;

    public EnhancedWardenEntity(EntityType<? extends EnhancedWardenEntity> entityType, Level level) {
        super(entityType, level);
        
        // Initialize AI components - with null checks for safety
        try {
            this.enhancedBrain = new EnhancedWardenBrain(this);
            this.advancedPathfinder = new AdvancedPathfinder(this);
            this.performanceMonitor = new PerformanceMonitor();
        } catch (Exception e) {
            // Fallback to basic initialization if there are issues
            this.enhancedBrain = null;
            this.advancedPathfinder = null;
            this.performanceMonitor = null;
        }
        
        // Set initial AI intensity from config
        this.entityData.set(AI_INTENSITY, WardenAIConfig.aiBehaviorIntensity);
        this.entityData.set(BUILDING_MODE, false);
        
        // Configure entity properties
        this.xpReward = 50; // Same as vanilla Warden

        // Apply config multipliers to attributes
        this.applyConfigMultipliers();
    }

    private void applyConfigMultipliers() {
        // Apply config multipliers to attributes after entity creation
        if (this.getAttribute(Attributes.MAX_HEALTH) != null) {
            this.getAttribute(Attributes.MAX_HEALTH).setBaseValue(500.0D * WardenAIConfig.healthMultiplier);
        }
        if (this.getAttribute(Attributes.MOVEMENT_SPEED) != null) {
            this.getAttribute(Attributes.MOVEMENT_SPEED).setBaseValue(0.3D * WardenAIConfig.speedMultiplier);
        }
        if (this.getAttribute(Attributes.ATTACK_DAMAGE) != null) {
            this.getAttribute(Attributes.ATTACK_DAMAGE).setBaseValue(30.0D * WardenAIConfig.damageMultiplier);
        }

        // Set health to max health after applying multiplier
        this.setHealth(this.getMaxHealth());
    }

    public static AttributeSupplier.Builder createAttributes() {
        return Monster.createMonsterAttributes()
            .add(Attributes.MAX_HEALTH, 500.0D)
            .add(Attributes.MOVEMENT_SPEED, 0.3D)
            .add(Attributes.KNOCKBACK_RESISTANCE, 1.0D)
            .add(Attributes.ATTACK_KNOCKBACK, 1.5D)
            .add(Attributes.ATTACK_DAMAGE, 30.0D)
            .add(Attributes.FOLLOW_RANGE, 24.0D);
    }

    @Override
    protected void defineSynchedData() {
        super.defineSynchedData();
        this.entityData.define(AI_INTENSITY, 5);
        this.entityData.define(BUILDING_MODE, false);
    }

    @Override
    protected void registerGoals() {
        try {
            // Basic goals
            this.goalSelector.addGoal(0, new FloatGoal(this));

            // Enhanced AI goals - only add if config is available and enabled
            if (WardenAIConfig.enableBlockPlacement) {
                this.goalSelector.addGoal(1, new IntelligentBlockPlacementGoal(this));
            }

            this.goalSelector.addGoal(2, new MeleeAttackGoal(this, 1.0D, false));
            this.goalSelector.addGoal(7, new WaterAvoidingRandomStrollGoal(this, 1.0D));
            this.goalSelector.addGoal(8, new LookAtPlayerGoal(this, Player.class, 8.0F));
            this.goalSelector.addGoal(8, new RandomLookAroundGoal(this));

            // Target goals
            this.targetSelector.addGoal(1, new HurtByTargetGoal(this));
            this.targetSelector.addGoal(2, new NearestAttackableTargetGoal<>(this, Player.class, true));
        } catch (Exception e) {
            // Fallback to basic goals only
            this.goalSelector.addGoal(0, new FloatGoal(this));
            this.goalSelector.addGoal(2, new MeleeAttackGoal(this, 1.0D, false));
            this.targetSelector.addGoal(1, new HurtByTargetGoal(this));
            this.targetSelector.addGoal(2, new NearestAttackableTargetGoal<>(this, Player.class, true));
        }
    }

    @Override
    public void tick() {
        if (WardenAIConfig.enablePerformanceMonitoring) {
            this.tickStartTime = System.nanoTime();
            this.aiCalculationsThisTick = 0;
        }
        
        super.tick();
        
        if (!this.level().isClientSide) {
            this.serverTick();
        }
        
        // Performance monitoring
        if (WardenAIConfig.enablePerformanceMonitoring) {
            long tickTime = (System.nanoTime() - this.tickStartTime) / 1_000_000; // Convert to milliseconds
            this.performanceMonitor.recordTickTime(tickTime);
            
            if (tickTime > WardenAIConfig.performanceThreshold) {
                // Throttle AI if performance threshold exceeded
                this.throttleAI();
            }
        }
    }

    private void serverTick() {
        // Update cooldowns
        if (this.blockPlacementCooldown > 0) {
            this.blockPlacementCooldown--;
        }
        
        // Enhanced AI decision making
        long currentTime = this.level().getGameTime();
        long decisionInterval = (long)(WardenAIConfig.aiDecisionFrequency * 20); // Convert seconds to ticks
        
        if (currentTime - this.lastAIDecision >= decisionInterval) {
            this.makeAIDecision();
            this.lastAIDecision = currentTime;
        }
        
        // Clean up old placed blocks
        if (WardenAIConfig.autoCleanupBlocks && !this.placedBlocks.isEmpty()) {
            this.cleanupOldBlocks();
        }
    }

    private void makeAIDecision() {
        if (this.aiCalculationsThisTick >= WardenAIConfig.maxAiCalculationsPerTick) {
            return; // Throttle AI calculations
        }
        
        this.aiCalculationsThisTick++;
        
        // Enhanced brain processing
        if (this.enhancedBrain != null) {
            this.enhancedBrain.tick();
        }

        // Advanced pathfinding updates
        if (this.getTarget() != null && this.advancedPathfinder != null) {
            this.advancedPathfinder.updatePath(this.getTarget());
        }
    }

    private void throttleAI() {
        // Reduce AI intensity temporarily to maintain performance
        int currentIntensity = this.entityData.get(AI_INTENSITY);
        if (currentIntensity > 1) {
            this.entityData.set(AI_INTENSITY, Math.max(1, currentIntensity - 1));
        }
    }

    private void cleanupOldBlocks() {
        if (this.level() instanceof net.minecraft.server.level.ServerLevel serverLevel) {
            BlockCleanupManager.getInstance().processCleanup(serverLevel);
        }
    }

    @Override
    public boolean doHurtTarget(net.minecraft.world.entity.Entity target) {
        boolean result = super.doHurtTarget(target);
        
        // Apply effects but respect accessibility settings
        if (result && target instanceof LivingEntity livingTarget) {
            // Don't apply blindness if disabled in config
            if (!WardenAIConfig.disableBlindnessEffect) {
                livingTarget.addEffect(new MobEffectInstance(MobEffects.BLINDNESS, 200), this);
            }
            
            // Apply other effects (darkness, slowness)
            livingTarget.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SLOWDOWN, 200, 2), this);
        }
        
        return result;
    }

    @Override
    protected SoundEvent getAmbientSound() {
        return SoundEvents.WARDEN_AMBIENT;
    }

    @Override
    protected SoundEvent getHurtSound(DamageSource damageSource) {
        return SoundEvents.WARDEN_HURT;
    }

    @Override
    protected SoundEvent getDeathSound() {
        return SoundEvents.WARDEN_DEATH;
    }

    @Override
    protected void playStepSound(BlockPos pos, BlockState state) {
        this.playSound(SoundEvents.WARDEN_STEP, 0.15F, 1.0F);
    }

    // Getters and setters for AI components
    public EnhancedWardenBrain getEnhancedBrain() {
        return this.enhancedBrain;
    }

    public AdvancedPathfinder getAdvancedPathfinder() {
        return this.advancedPathfinder;
    }

    public List<BlockPos> getPlacedBlocks() {
        return this.placedBlocks;
    }

    public boolean canPlaceBlocks() {
        return WardenAIConfig.enableBlockPlacement && 
               this.blockPlacementCooldown <= 0 && 
               this.placedBlocks.size() < WardenAIConfig.maxBlocksPlaced;
    }

    public void setBlockPlacementCooldown() {
        this.blockPlacementCooldown = WardenAIConfig.blockPlacementCooldown;
    }

    public boolean isInBuildingMode() {
        return this.entityData.get(BUILDING_MODE);
    }

    public void setBuildingMode(boolean buildingMode) {
        this.entityData.set(BUILDING_MODE, buildingMode);
    }

    public int getAIIntensity() {
        return this.entityData.get(AI_INTENSITY);
    }

    @Override
    public void addAdditionalSaveData(CompoundTag compound) {
        super.addAdditionalSaveData(compound);
        compound.putInt("AIIntensity", this.getAIIntensity());
        compound.putBoolean("BuildingMode", this.isInBuildingMode());
        
        // Save placed blocks for cleanup
        CompoundTag placedBlocksTag = new CompoundTag();
        for (int i = 0; i < this.placedBlocks.size(); i++) {
            BlockPos pos = this.placedBlocks.get(i);
            placedBlocksTag.putLong("block_" + i, pos.asLong());
        }
        compound.put("PlacedBlocks", placedBlocksTag);
    }

    @Override
    public void readAdditionalSaveData(CompoundTag compound) {
        super.readAdditionalSaveData(compound);
        this.entityData.set(AI_INTENSITY, compound.getInt("AIIntensity"));
        this.entityData.set(BUILDING_MODE, compound.getBoolean("BuildingMode"));
        
        // Load placed blocks
        this.placedBlocks.clear();
        CompoundTag placedBlocksTag = compound.getCompound("PlacedBlocks");
        for (String key : placedBlocksTag.getAllKeys()) {
            if (key.startsWith("block_")) {
                long posLong = placedBlocksTag.getLong(key);
                this.placedBlocks.add(BlockPos.of(posLong));
            }
        }
    }
}

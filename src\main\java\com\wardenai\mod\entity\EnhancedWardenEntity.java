package com.wardenai.mod.entity;

import com.wardenai.mod.ai.EnhancedWardenBrain;
import com.wardenai.mod.ai.goals.IntelligentBlockPlacementGoal;
import com.wardenai.mod.ai.pathfinding.AdvancedPathfinder;
import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.util.PerformanceMonitor;
import com.wardenai.mod.util.BlockCleanupManager;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.monster.warden.Warden;
import net.minecraft.world.entity.ai.Brain;
import com.mojang.serialization.Dynamic;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.Vec3;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

/**
 * Enhanced Warden Entity with advanced AI capabilities
 * 
 * Features:
 * - Advanced pathfinding and decision-making algorithms
 * - Intelligent block placement for reaching players
 * - Improved accessibility (no blindness effect)
 * - Performance-optimized AI systems
 * - Configurable behavior intensity
 */
public class EnhancedWardenEntity extends Warden {
    
    // Data accessors for syncing data between client and server
    private static final EntityDataAccessor<Integer> AI_INTENSITY = 
        SynchedEntityData.defineId(EnhancedWardenEntity.class, EntityDataSerializers.INT);
    private static final EntityDataAccessor<Boolean> BUILDING_MODE = 
        SynchedEntityData.defineId(EnhancedWardenEntity.class, EntityDataSerializers.BOOLEAN);
    
    // AI and pathfinding components
    private EnhancedWardenBrain enhancedBrain;
    private AdvancedPathfinder advancedPathfinder;
    private PerformanceMonitor performanceMonitor;
    
    // Block placement tracking
    private List<BlockPos> placedBlocks = new ArrayList<>();
    private int blockPlacementCooldown = 0;
    private long lastAIDecision = 0;
    
    // Performance tracking
    private int aiCalculationsThisTick = 0;
    private long tickStartTime = 0;

    public EnhancedWardenEntity(EntityType<? extends EnhancedWardenEntity> entityType, Level level) {
        super(entityType, level);
        
        // Initialize AI components - with null checks for safety
        try {
            this.enhancedBrain = new EnhancedWardenBrain(this);
            this.advancedPathfinder = new AdvancedPathfinder(this);
            this.performanceMonitor = new PerformanceMonitor();
        } catch (Exception e) {
            // Fallback to basic initialization if there are issues
            this.enhancedBrain = null;
            this.advancedPathfinder = null;
            this.performanceMonitor = null;
        }
        
        // Set initial AI intensity from config
        this.entityData.set(AI_INTENSITY, WardenAIConfig.aiBehaviorIntensity);
        this.entityData.set(BUILDING_MODE, false);
        
        // Configure entity properties
        this.xpReward = 50; // Same as vanilla Warden

        // Apply config multipliers to attributes
        this.applyConfigMultipliers();
    }

    private void applyConfigMultipliers() {
        // Apply config multipliers to attributes after entity creation
        if (this.getAttribute(Attributes.MAX_HEALTH) != null) {
            this.getAttribute(Attributes.MAX_HEALTH).setBaseValue(500.0D * WardenAIConfig.healthMultiplier);
        }
        if (this.getAttribute(Attributes.MOVEMENT_SPEED) != null) {
            this.getAttribute(Attributes.MOVEMENT_SPEED).setBaseValue(0.3D * WardenAIConfig.speedMultiplier);
        }
        if (this.getAttribute(Attributes.ATTACK_DAMAGE) != null) {
            this.getAttribute(Attributes.ATTACK_DAMAGE).setBaseValue(30.0D * WardenAIConfig.damageMultiplier);
        }

        // Set health to max health after applying multiplier
        this.setHealth(this.getMaxHealth());
    }

    public static AttributeSupplier.Builder createAttributes() {
        return Warden.createAttributes()
            .add(Attributes.MAX_HEALTH, 500.0D)
            .add(Attributes.MOVEMENT_SPEED, 0.3D)
            .add(Attributes.KNOCKBACK_RESISTANCE, 1.0D)
            .add(Attributes.ATTACK_KNOCKBACK, 1.5D)
            .add(Attributes.ATTACK_DAMAGE, 30.0D)
            .add(Attributes.FOLLOW_RANGE, 24.0D);
    }

    @Override
    protected void defineSynchedData() {
        super.defineSynchedData();
        this.entityData.define(AI_INTENSITY, 5);
        this.entityData.define(BUILDING_MODE, false);
    }

    @Override
    protected void registerGoals() {
        // Clear any inherited goals from Warden
        this.goalSelector.removeAllGoals(goal -> true);
        this.targetSelector.removeAllGoals(goal -> true);

        try {
            // Basic goals
            this.goalSelector.addGoal(0, new FloatGoal(this));

            // Enhanced AI goals - only add if config is available and enabled
            if (WardenAIConfig.enableBlockPlacement) {
                this.goalSelector.addGoal(1, new IntelligentBlockPlacementGoal(this));
                if (WardenAIConfig.debugMode) {
                    System.out.println("Enhanced Warden: Added IntelligentBlockPlacementGoal");
                }
            }

            this.goalSelector.addGoal(2, new MeleeAttackGoal(this, 1.0D, false));
            this.goalSelector.addGoal(7, new WaterAvoidingRandomStrollGoal(this, 1.0D));
            this.goalSelector.addGoal(8, new LookAtPlayerGoal(this, Player.class, 8.0F));
            this.goalSelector.addGoal(8, new RandomLookAroundGoal(this));

            // Target goals
            this.targetSelector.addGoal(1, new HurtByTargetGoal(this));
            this.targetSelector.addGoal(2, new NearestAttackableTargetGoal<>(this, Player.class, true));

            if (WardenAIConfig.debugMode) {
                System.out.println("Enhanced Warden: Successfully registered " + this.goalSelector.getAvailableGoals().size() + " goals");
            }
        } catch (Exception e) {
            System.err.println("Enhanced Warden: Error registering goals: " + e.getMessage());
            // Fallback to basic goals only
            this.goalSelector.addGoal(0, new FloatGoal(this));
            this.goalSelector.addGoal(2, new MeleeAttackGoal(this, 1.0D, false));
            this.targetSelector.addGoal(1, new HurtByTargetGoal(this));
            this.targetSelector.addGoal(2, new NearestAttackableTargetGoal<>(this, Player.class, true));
        }
    }

    @Override
    public void tick() {
        if (WardenAIConfig.enablePerformanceMonitoring) {
            this.tickStartTime = System.nanoTime();
            this.aiCalculationsThisTick = 0;
        }
        
        super.tick();
        
        if (!this.level().isClientSide) {
            this.serverTick();
        }
        
        // Performance monitoring
        if (WardenAIConfig.enablePerformanceMonitoring) {
            long tickTime = (System.nanoTime() - this.tickStartTime) / 1_000_000; // Convert to milliseconds
            this.performanceMonitor.recordTickTime(tickTime);
            
            if (tickTime > WardenAIConfig.performanceThreshold) {
                // Throttle AI if performance threshold exceeded
                this.throttleAI();
            }
        }
    }

    private void serverTick() {
        // Update cooldowns
        if (this.blockPlacementCooldown > 0) {
            this.blockPlacementCooldown--;
        }
        
        // Enhanced AI decision making
        long currentTime = this.level().getGameTime();
        long decisionInterval = (long)(WardenAIConfig.aiDecisionFrequency * 20); // Convert seconds to ticks
        
        if (currentTime - this.lastAIDecision >= decisionInterval) {
            this.makeAIDecision();
            this.lastAIDecision = currentTime;
        }
        
        // Clean up old placed blocks
        if (WardenAIConfig.autoCleanupBlocks && !this.placedBlocks.isEmpty()) {
            this.cleanupOldBlocks();
        }
    }

    private void makeAIDecision() {
        if (this.aiCalculationsThisTick >= WardenAIConfig.maxAiCalculationsPerTick) {
            return; // Throttle AI calculations
        }
        
        this.aiCalculationsThisTick++;
        
        // Enhanced brain processing
        if (this.enhancedBrain != null) {
            this.enhancedBrain.tick();

            // Debug logging
            if (WardenAIConfig.debugMode && this.getTarget() != null) {
                System.out.println("Enhanced Warden AI: Target=" + this.getTarget().getName().getString() +
                                 ", State=" + this.enhancedBrain.getCurrentState() +
                                 ", Building=" + this.isInBuildingMode());
            }
        }

        // Advanced pathfinding updates
        if (this.getTarget() != null && this.advancedPathfinder != null) {
            this.advancedPathfinder.updatePath(this.getTarget());
        }
    }

    private void throttleAI() {
        // Reduce AI intensity temporarily to maintain performance
        int currentIntensity = this.entityData.get(AI_INTENSITY);
        if (currentIntensity > 1) {
            this.entityData.set(AI_INTENSITY, Math.max(1, currentIntensity - 1));
        }
    }

    private void cleanupOldBlocks() {
        if (this.level() instanceof net.minecraft.server.level.ServerLevel serverLevel) {
            BlockCleanupManager.getInstance().processCleanup(serverLevel);
        }
    }

    @Override
    public boolean doHurtTarget(net.minecraft.world.entity.Entity target) {
        // Call Entity.doHurtTarget instead of super to bypass Warden's effect application
        boolean result = target.hurt(this.damageSources().mobAttack(this), (float)this.getAttributeValue(Attributes.ATTACK_DAMAGE));

        // Enhanced Wardens don't apply any visual impairment effects for accessibility
        if (result && target instanceof LivingEntity livingTarget) {
            // NO darkness or blindness effects - Enhanced Wardens are accessibility-friendly

            // Only apply non-visual effects (slowness for tactical gameplay)
            livingTarget.addEffect(new MobEffectInstance(MobEffects.MOVEMENT_SLOWDOWN, 100, 1), this);

            if (WardenAIConfig.debugMode) {
                System.out.println("Enhanced Warden: Melee attack hit, NO darkness effect applied (accessibility mode)");
            }
        }

        return result;
    }

    @Override
    protected Brain<?> makeBrain(Dynamic<?> dynamic) {
        // Use the vanilla Warden brain but we'll override specific behaviors later
        // This ensures all required memory modules are present
        Brain<?> brain = super.makeBrain(dynamic);

        if (WardenAIConfig.debugMode) {
            System.out.println("Enhanced Warden: Created brain with accessibility overrides");
        }

        return brain;
    }

    // Custom method to control ranged attack behavior
    public boolean shouldUseRangedAttack(LivingEntity target) {
        // Enhanced Wardens prefer building over ranged attacks
        if (WardenAIConfig.enableBlockPlacement && target != null) {
            double heightDiff = target.getY() - this.getY();
            if (heightDiff > 1.0) {
                // Force building instead of sonic boom
                this.setBuildingMode(true);
                if (WardenAIConfig.debugMode) {
                    System.out.println("Enhanced Warden: Blocked ranged attack, forcing building mode");
                }
                return false;
            }
        }

        // Only allow ranged attack very rarely (5% chance)
        boolean allowRanged = this.random.nextFloat() < 0.05f;
        if (WardenAIConfig.debugMode && !allowRanged) {
            System.out.println("Enhanced Warden: Blocked ranged attack (accessibility mode)");
        }
        return allowRanged;
    }

    // Try to intercept sonic boom behavior through brain activities
    @Override
    public void customServerAiStep() {
        // Allow vanilla Warden brain to run but intercept sonic boom
        super.customServerAiStep();

        // Immediately remove any darkness effects that might have been applied
        if (this.getTarget() instanceof Player player) {
            if (player.hasEffect(MobEffects.DARKNESS)) {
                player.removeEffect(MobEffects.DARKNESS);
                if (WardenAIConfig.debugMode) {
                    System.out.println("Enhanced Warden: Removed darkness effect for accessibility");
                }
            }
        }

        // Force building behavior check every few ticks
        if (this.tickCount % 20 == 0) { // Check every second
            this.checkAndForceBuildingMode();
        }

        // Debug logging
        if (WardenAIConfig.debugMode && this.tickCount % 100 == 0) {
            System.out.println("Enhanced Warden: Running hybrid AI - vanilla brain + accessibility overrides");
        }
    }



    private void checkAndForceBuildingMode() {
        LivingEntity target = this.getTarget();
        if (target == null) {
            // Don't immediately disable building mode - keep trying for a bit
            if (this.tickCount % 100 == 0) { // Only disable after 5 seconds of no target
                this.setBuildingMode(false);
            }
            return;
        }

        double heightDiff = target.getY() - this.getY();
        double distance = this.distanceTo(target);

        // More aggressive building triggers
        boolean shouldBuild = false;
        String reason = "";

        if (heightDiff > 2.0 && distance < 20.0 && WardenAIConfig.enableBlockPlacement) {
            shouldBuild = true;
            reason = "Target is " + String.format("%.1f", heightDiff) + " blocks higher";
        } else if (distance > 8.0 && distance < 16.0 && !this.hasLineOfSight(target) && WardenAIConfig.enableBlockPlacement) {
            shouldBuild = true;
            reason = "Target blocked by obstacles, building to reach";
        } else if (this.isInBuildingMode() && heightDiff > 1.0 && distance < 25.0) {
            // Continue building if already started and target is still elevated
            shouldBuild = true;
            reason = "Continuing building - target still elevated";
        }

        if (shouldBuild && !this.isInBuildingMode()) {
            this.setBuildingMode(true);
            if (WardenAIConfig.debugMode) {
                System.out.println("Enhanced Warden: BUILDING MODE ACTIVATED! " + reason);
            }
        } else if (!shouldBuild && heightDiff <= 1.0 && distance < 4.0) {
            // Only stop building if we're very close and at same level
            this.setBuildingMode(false);
            if (WardenAIConfig.debugMode) {
                System.out.println("Enhanced Warden: Building mode disabled - target reached");
            }
        }
    }

    // Try to disable sonic boom through various methods
    public boolean canTargetEntity(LivingEntity target) {
        // Disable ranged targeting to prevent sonic boom
        return false;
    }



    @Override
    protected SoundEvent getAmbientSound() {
        return SoundEvents.WARDEN_AMBIENT;
    }

    @Override
    protected SoundEvent getHurtSound(DamageSource damageSource) {
        return SoundEvents.WARDEN_HURT;
    }

    @Override
    protected SoundEvent getDeathSound() {
        return SoundEvents.WARDEN_DEATH;
    }

    @Override
    protected void playStepSound(BlockPos pos, BlockState state) {
        this.playSound(SoundEvents.WARDEN_STEP, 0.15F, 1.0F);
    }

    // Getters and setters for AI components
    public EnhancedWardenBrain getEnhancedBrain() {
        return this.enhancedBrain;
    }

    public AdvancedPathfinder getAdvancedPathfinder() {
        return this.advancedPathfinder;
    }

    public List<BlockPos> getPlacedBlocks() {
        return this.placedBlocks;
    }

    public boolean canPlaceBlocks() {
        return WardenAIConfig.enableBlockPlacement && 
               this.blockPlacementCooldown <= 0 && 
               this.placedBlocks.size() < WardenAIConfig.maxBlocksPlaced;
    }

    public void setBlockPlacementCooldown() {
        this.blockPlacementCooldown = WardenAIConfig.blockPlacementCooldown;
    }

    public boolean isInBuildingMode() {
        return this.entityData.get(BUILDING_MODE);
    }

    public void setBuildingMode(boolean buildingMode) {
        this.entityData.set(BUILDING_MODE, buildingMode);
    }

    public int getAIIntensity() {
        return this.entityData.get(AI_INTENSITY);
    }

    @Override
    public void addAdditionalSaveData(CompoundTag compound) {
        super.addAdditionalSaveData(compound);
        compound.putInt("AIIntensity", this.getAIIntensity());
        compound.putBoolean("BuildingMode", this.isInBuildingMode());
        
        // Save placed blocks for cleanup
        CompoundTag placedBlocksTag = new CompoundTag();
        for (int i = 0; i < this.placedBlocks.size(); i++) {
            BlockPos pos = this.placedBlocks.get(i);
            placedBlocksTag.putLong("block_" + i, pos.asLong());
        }
        compound.put("PlacedBlocks", placedBlocksTag);
    }

    @Override
    public void readAdditionalSaveData(CompoundTag compound) {
        super.readAdditionalSaveData(compound);
        this.entityData.set(AI_INTENSITY, compound.getInt("AIIntensity"));
        this.entityData.set(BUILDING_MODE, compound.getBoolean("BuildingMode"));
        
        // Load placed blocks
        this.placedBlocks.clear();
        CompoundTag placedBlocksTag = compound.getCompound("PlacedBlocks");
        for (String key : placedBlocksTag.getAllKeys()) {
            if (key.startsWith("block_")) {
                long posLong = placedBlocksTag.getLong(key);
                this.placedBlocks.add(BlockPos.of(posLong));
            }
        }
    }
}

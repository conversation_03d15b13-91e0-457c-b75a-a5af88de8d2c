package com.wardenai.mod.client;

import com.wardenai.mod.WardenAIMod;
import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import net.minecraft.client.Minecraft;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.RenderLevelStageEvent;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.event.entity.living.MobEffectEvent;
import net.minecraftforge.eventbus.api.Event;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Visual Accessibility Handler for Enhanced Warden
 * 
 * Handles visual accessibility features including:
 * - Removing/disabling blindness effects from Enhanced Wardens
 * - Adding visual indicators to help players track Enhanced Wardens
 * - Providing clear visibility during encounters
 * - Optional particle effects for pathfinding visualization
 */
//@Mod.EventBusSubscriber(modid = WardenAIMod.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
public class VisualAccessibilityHandler {

    private static boolean initialized = false;
    
    // Tracking Enhanced Wardens for visual indicators
    private static final Map<UUID, EnhancedWardenTracker> trackedWardens = new HashMap<>();
    private static int particleTimer = 0;
    
    // Visual indicator settings
    private static final int INDICATOR_RANGE = 32; // Range for visual indicators
    private static final int PARTICLE_INTERVAL = 10; // Ticks between particle spawns
    
    public static class EnhancedWardenTracker {
        public final UUID wardenId;
        public Vec3 lastPosition;
        public long lastSeen;
        public boolean isBuilding;
        public int indicatorTimer;
        
        public EnhancedWardenTracker(UUID id, Vec3 position) {
            this.wardenId = id;
            this.lastPosition = position;
            this.lastSeen = System.currentTimeMillis();
            this.isBuilding = false;
            this.indicatorTimer = 0;
        }
        
        public void update(Vec3 position, boolean building) {
            this.lastPosition = position;
            this.lastSeen = System.currentTimeMillis();
            this.isBuilding = building;
            this.indicatorTimer++;
        }
    }

    /**
     * Prevents blindness effects from Enhanced Wardens if disabled in config
     */
    @SubscribeEvent
    public static void onMobEffectApplicable(MobEffectEvent.Applicable event) {
        if (!WardenAIConfig.disableBlindnessEffect) {
            return;
        }
        
        // Check if the effect is blindness or darkness and the source is an Enhanced Warden
        MobEffectInstance effectInstance = event.getEffectInstance();
        if (effectInstance.getEffect() == MobEffects.BLINDNESS || effectInstance.getEffect() == MobEffects.DARKNESS) {
            Entity source = event.getEntity();
            
            // Check if there's an Enhanced Warden nearby that could be the source
            if (source instanceof Player player) {
                Level level = player.level();
                boolean hasEnhancedWardenNearby = level.getEntitiesOfClass(EnhancedWardenEntity.class, 
                    player.getBoundingBox().inflate(16.0))
                    .stream()
                    .anyMatch(warden -> warden.getTarget() == player);
                
                if (hasEnhancedWardenNearby) {
                    event.setResult(Event.Result.DENY);
                    
                    if (WardenAIConfig.debugMode) {
                        System.out.println("Blocked " + effectInstance.getEffect().getDescriptionId() + " effect from Enhanced Warden for accessibility");
                    }
                }
            }
        }
    }

    /**
     * Client tick event for visual indicators and tracking
     */
    @SubscribeEvent
    public static void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) {
            return;
        }

        try {
            Minecraft minecraft = Minecraft.getInstance();
            if (minecraft == null) {
                return;
            }

            LocalPlayer player = minecraft.player;
            Level level = minecraft.level;

            if (player == null || level == null) {
                return;
            }

            if (!initialized) {
                initialized = true;
            }

            // Update Enhanced Warden tracking
            updateWardenTracking(level, player);

            // Handle visual indicators
            if (WardenAIConfig.enhancedVisibilityIndicators) {
                handleVisualIndicators(level, player);
            }

            // Handle pathfinding particles for debug
            if (WardenAIConfig.showPathfindingParticles && WardenAIConfig.debugMode) {
                handlePathfindingParticles(level, player);
            }

            particleTimer++;
        } catch (Exception e) {
            // Silently catch any client-side errors to prevent crashes
            if (WardenAIConfig.debugMode) {
                System.err.println("Error in VisualAccessibilityHandler client tick: " + e.getMessage());
            }
        }
    }

    /**
     * Updates tracking information for Enhanced Wardens
     */
    private static void updateWardenTracking(Level level, Player player) {
        // Find all Enhanced Wardens within range
        level.getEntitiesOfClass(EnhancedWardenEntity.class, 
            player.getBoundingBox().inflate(INDICATOR_RANGE))
            .forEach(warden -> {
                UUID wardenId = warden.getUUID();
                Vec3 wardenPos = warden.position();
                boolean isBuilding = warden.isInBuildingMode();
                
                EnhancedWardenTracker tracker = trackedWardens.get(wardenId);
                if (tracker == null) {
                    tracker = new EnhancedWardenTracker(wardenId, wardenPos);
                    trackedWardens.put(wardenId, tracker);
                } else {
                    tracker.update(wardenPos, isBuilding);
                }
            });
        
        // Remove old trackers for Wardens that are no longer nearby
        trackedWardens.entrySet().removeIf(entry -> {
            long timeSinceLastSeen = System.currentTimeMillis() - entry.getValue().lastSeen;
            return timeSinceLastSeen > 5000; // Remove after 5 seconds
        });
    }

    /**
     * Handles visual indicators for Enhanced Wardens
     */
    private static void handleVisualIndicators(Level level, Player player) {
        for (EnhancedWardenTracker tracker : trackedWardens.values()) {
            Vec3 wardenPos = tracker.lastPosition;
            double distance = player.position().distanceTo(wardenPos);
            
            // Only show indicators within range
            if (distance > INDICATOR_RANGE) {
                continue;
            }
            
            // Spawn indicator particles
            if (tracker.indicatorTimer % PARTICLE_INTERVAL == 0) {
                spawnIndicatorParticles(level, wardenPos, tracker.isBuilding);
            }
            
            // Additional indicators for building mode
            if (tracker.isBuilding && tracker.indicatorTimer % (PARTICLE_INTERVAL / 2) == 0) {
                spawnBuildingIndicators(level, wardenPos);
            }
        }
    }

    /**
     * Spawns indicator particles around Enhanced Wardens
     */
    private static void spawnIndicatorParticles(Level level, Vec3 position, boolean isBuilding) {
        try {
            // Choose particle type based on state - use simpler particles to avoid compatibility issues
            var particleType = isBuilding ? ParticleTypes.SOUL : ParticleTypes.ENCHANT;

            // Spawn particles in a circle around the Warden
            for (int i = 0; i < 8; i++) {
                double angle = (i / 8.0) * 2 * Math.PI;
                double radius = 2.0;

                double x = position.x + Math.cos(angle) * radius;
                double z = position.z + Math.sin(angle) * radius;
                double y = position.y + 1.5;

                level.addParticle(particleType, x, y, z, 0, 0.1, 0);
            }
        } catch (Exception e) {
            // Silently handle particle errors
        }
    }

    /**
     * Spawns building indicator particles
     */
    private static void spawnBuildingIndicators(Level level, Vec3 position) {
        try {
            // Spawn upward-moving particles to indicate building activity
            for (int i = 0; i < 3; i++) {
                double offsetX = (Math.random() - 0.5) * 2.0;
                double offsetZ = (Math.random() - 0.5) * 2.0;

                level.addParticle(ParticleTypes.FLAME,
                    position.x + offsetX,
                    position.y + 0.5,
                    position.z + offsetZ,
                    0, 0.2, 0);
            }
        } catch (Exception e) {
            // Silently handle particle errors
        }
    }

    /**
     * Handles pathfinding visualization particles for debug mode
     */
    private static void handlePathfindingParticles(Level level, Player player) {
        if (particleTimer % (PARTICLE_INTERVAL * 2) != 0) {
            return;
        }
        
        // Find Enhanced Wardens and show their pathfinding
        level.getEntitiesOfClass(EnhancedWardenEntity.class, 
            player.getBoundingBox().inflate(32.0))
            .forEach(warden -> {
                if (warden.getTarget() != null) {
                    showPathfindingVisualization(level, warden);
                }
            });
    }

    /**
     * Shows pathfinding visualization for debug purposes
     */
    private static void showPathfindingVisualization(Level level, EnhancedWardenEntity warden) {
        var pathfinder = warden.getAdvancedPathfinder();
        var lastResult = pathfinder.getLastResult();
        
        if (lastResult != null && !lastResult.waypoints.isEmpty()) {
            // Show waypoints
            for (Vec3 waypoint : lastResult.waypoints) {
                level.addParticle(ParticleTypes.END_ROD, 
                    waypoint.x, waypoint.y + 0.5, waypoint.z, 
                    0, 0, 0);
            }
            
            // Show required blocks for building paths
            if (lastResult.requiresBuilding && !lastResult.requiredBlocks.isEmpty()) {
                lastResult.requiredBlocks.forEach(blockPos -> {
                    level.addParticle(ParticleTypes.FLAME, 
                        blockPos.getX() + 0.5, blockPos.getY() + 0.5, blockPos.getZ() + 0.5, 
                        0, 0.1, 0);
                });
            }
        }
        
        // Show current target
        if (warden.getTarget() != null) {
            Vec3 targetPos = warden.getTarget().position();
            level.addParticle(ParticleTypes.HEART, 
                targetPos.x, targetPos.y + 2.0, targetPos.z, 
                0, 0, 0);
        }
    }

    /**
     * Render level stage event for additional visual enhancements
     */
    @SubscribeEvent
    public static void onRenderLevelStage(RenderLevelStageEvent event) {
        if (event.getStage() != RenderLevelStageEvent.Stage.AFTER_PARTICLES) {
            return;
        }
        
        if (!WardenAIConfig.enhancedVisibilityIndicators) {
            return;
        }
        
        // Additional rendering could be added here for more complex visual indicators
        // For now, we rely on particle effects
    }

    /**
     * Clears all tracking data (useful for cleanup)
     */
    public static void clearTracking() {
        trackedWardens.clear();
    }

    /**
     * Gets the number of currently tracked Enhanced Wardens
     */
    public static int getTrackedWardenCount() {
        return trackedWardens.size();
    }

    /**
     * Checks if a specific Enhanced Warden is being tracked
     */
    public static boolean isWardenTracked(UUID wardenId) {
        return trackedWardens.containsKey(wardenId);
    }

    /**
     * Gets tracking information for a specific Enhanced Warden
     */
    public static EnhancedWardenTracker getWardenTracker(UUID wardenId) {
        return trackedWardens.get(wardenId);
    }
}

package com.wardenai.mod.ai.pathfinding;

import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.pathfinder.Path;
import net.minecraft.world.phys.Vec3;

import java.util.*;

/**
 * Advanced Pathfinder for Enhanced Warden
 * 
 * Implements sophisticated pathfinding algorithms that go beyond vanilla
 * Minecraft pathfinding. Includes 3D pathfinding, obstacle analysis,
 * and intelligent route planning for complex terrain.
 */
public class AdvancedPathfinder {
    
    private final EnhancedWardenEntity warden;
    private final Level level;
    
    // Pathfinding state
    private PathfindingResult lastResult;
    private long lastPathCalculation = 0;
    private LivingEntity currentPathTarget;
    
    // Performance optimization
    private final Map<String, PathfindingResult> pathCache = new HashMap<>();
    private final int maxCacheSize = 50;
    private final long cacheExpirationTime = 100; // 5 seconds in ticks

    public enum PathType {
        DIRECT,           // Straight line path
        GROUND_LEVEL,     // Standard ground pathfinding
        ELEVATED,         // Path requiring elevation changes
        BUILDING_REQUIRED, // Path requiring block placement
        IMPOSSIBLE        // No viable path found
    }

    public static class PathfindingResult {
        public final PathType pathType;
        public final List<Vec3> waypoints;
        public final List<BlockPos> requiredBlocks;
        public final double estimatedTime;
        public final double difficulty;
        public final boolean requiresBuilding;
        public final long calculationTime;

        public PathfindingResult(PathType type, List<Vec3> waypoints, List<BlockPos> blocks,
                               double time, double difficulty, boolean building, long calcTime) {
            this.pathType = type;
            this.waypoints = waypoints != null ? waypoints : new ArrayList<>();
            this.requiredBlocks = blocks != null ? blocks : new ArrayList<>();
            this.estimatedTime = time;
            this.difficulty = difficulty;
            this.requiresBuilding = building;
            this.calculationTime = calcTime;
        }
    }

    public AdvancedPathfinder(EnhancedWardenEntity warden) {
        this.warden = warden;
        this.level = warden.level();
    }

    /**
     * Updates the path to the target using advanced algorithms
     */
    public void updatePath(LivingEntity target) {
        if (target == null) {
            return;
        }

        long currentTime = level.getGameTime();
        
        // Check if we need to recalculate the path
        if (shouldRecalculatePath(target, currentTime)) {
            PathfindingResult result = calculateAdvancedPath(warden.position(), target.position());
            
            if (result != null) {
                this.lastResult = result;
                this.lastPathCalculation = currentTime;
                this.currentPathTarget = target;
                
                // Apply the pathfinding result
                applyPathfindingResult(result);
            }
        }
    }

    /**
     * Analyzes complex pathfinding scenarios for high-intensity AI
     */
    public void analyzeComplexPath(LivingEntity target) {
        if (target == null || warden.getAIIntensity() < 5) {
            return;
        }

        Vec3 start = warden.position();
        Vec3 end = target.position();
        
        // Multi-algorithm pathfinding analysis
        List<PathfindingResult> alternatives = new ArrayList<>();
        
        // Try different pathfinding approaches
        alternatives.add(calculateDirectPath(start, end));
        alternatives.add(calculateGroundPath(start, end));
        alternatives.add(calculateElevatedPath(start, end));
        
        if (WardenAIConfig.enableBlockPlacement) {
            alternatives.add(calculateBuildingPath(start, end));
        }
        
        // Select the best path based on multiple criteria
        PathfindingResult bestPath = selectOptimalPath(alternatives);
        
        if (bestPath != null && bestPath.pathType != PathType.IMPOSSIBLE) {
            this.lastResult = bestPath;
            applyPathfindingResult(bestPath);
        }
    }

    private boolean shouldRecalculatePath(LivingEntity target, long currentTime) {
        // Recalculate if:
        // 1. No previous calculation
        // 2. Target changed
        // 3. Enough time has passed
        // 4. Target moved significantly
        
        if (lastResult == null || currentPathTarget != target) {
            return true;
        }
        
        long timeSinceLastCalc = currentTime - lastPathCalculation;
        int recalcInterval = Math.max(10, 60 - (warden.getAIIntensity() * 5)); // Higher intensity = more frequent recalc
        
        if (timeSinceLastCalc >= recalcInterval) {
            return true;
        }
        
        // Check if target moved significantly
        if (currentPathTarget != null && lastResult.waypoints.size() > 0) {
            Vec3 lastTargetPos = lastResult.waypoints.get(lastResult.waypoints.size() - 1);
            double distanceMoved = target.position().distanceTo(lastTargetPos);
            return distanceMoved > 3.0; // Recalculate if target moved more than 3 blocks
        }
        
        return false;
    }

    private PathfindingResult calculateAdvancedPath(Vec3 start, Vec3 end) {
        long startTime = System.nanoTime();
        
        // Check cache first
        String cacheKey = getCacheKey(start, end);
        PathfindingResult cached = pathCache.get(cacheKey);
        if (cached != null && (level.getGameTime() - lastPathCalculation) < cacheExpirationTime) {
            return cached;
        }
        
        // Try different pathfinding algorithms in order of preference
        PathfindingResult result = null;
        
        // 1. Try direct path first (fastest)
        result = calculateDirectPath(start, end);
        if (result.pathType != PathType.IMPOSSIBLE) {
            cacheResult(cacheKey, result);
            return result;
        }
        
        // 2. Try standard ground pathfinding
        result = calculateGroundPath(start, end);
        if (result.pathType != PathType.IMPOSSIBLE) {
            cacheResult(cacheKey, result);
            return result;
        }
        
        // 3. Try elevated pathfinding for higher AI intensities
        if (warden.getAIIntensity() >= 3) {
            result = calculateElevatedPath(start, end);
            if (result.pathType != PathType.IMPOSSIBLE) {
                cacheResult(cacheKey, result);
                return result;
            }
        }
        
        // 4. Try building path if enabled and AI intensity is high enough
        if (WardenAIConfig.enableBlockPlacement && warden.getAIIntensity() >= 5) {
            result = calculateBuildingPath(start, end);
            if (result.pathType != PathType.IMPOSSIBLE) {
                cacheResult(cacheKey, result);
                return result;
            }
        }
        
        // No viable path found
        long calcTime = (System.nanoTime() - startTime) / 1_000_000;
        result = new PathfindingResult(PathType.IMPOSSIBLE, null, null, 0, 0, false, calcTime);
        cacheResult(cacheKey, result);
        return result;
    }

    private PathfindingResult calculateDirectPath(Vec3 start, Vec3 end) {
        List<Vec3> waypoints = new ArrayList<>();
        waypoints.add(start);
        waypoints.add(end);
        
        double distance = start.distanceTo(end);
        boolean hasLineOfSight = warden.hasLineOfSight(warden.getTarget());
        
        if (hasLineOfSight && isPathClear(start, end)) {
            return new PathfindingResult(PathType.DIRECT, waypoints, null, distance / 0.3, 1.0, false, 0);
        }
        
        return new PathfindingResult(PathType.IMPOSSIBLE, null, null, 0, 0, false, 0);
    }

    private PathfindingResult calculateGroundPath(Vec3 start, Vec3 end) {
        // Use vanilla pathfinding as a base
        Path vanillaPath = warden.getNavigation().createPath(end.x, end.y, end.z, 0);
        
        if (vanillaPath != null && vanillaPath.canReach()) {
            List<Vec3> waypoints = new ArrayList<>();
            for (int i = 0; i < vanillaPath.getNodeCount(); i++) {
                net.minecraft.world.level.pathfinder.Node node = vanillaPath.getNode(i);
                waypoints.add(new Vec3(node.x, node.y, node.z));
            }
            
            double distance = start.distanceTo(end);
            return new PathfindingResult(PathType.GROUND_LEVEL, waypoints, null, distance / 0.3, 2.0, false, 0);
        }
        
        return new PathfindingResult(PathType.IMPOSSIBLE, null, null, 0, 0, false, 0);
    }

    private PathfindingResult calculateElevatedPath(Vec3 start, Vec3 end) {
        // Check if target is significantly elevated
        double heightDiff = end.y - start.y;
        if (heightDiff < 2.0) {
            return calculateGroundPath(start, end); // Not elevated enough
        }
        
        // Try to find a path that goes up gradually
        List<Vec3> waypoints = new ArrayList<>();
        waypoints.add(start);
        
        // Create intermediate waypoints that gradually increase in height
        Vec3 direction = end.subtract(start).normalize();
        double totalDistance = start.distanceTo(end);
        int steps = Math.max(3, (int)(totalDistance / 4.0)); // One waypoint every 4 blocks
        
        for (int i = 1; i < steps; i++) {
            double progress = (double)i / steps;
            Vec3 intermediate = start.add(direction.scale(totalDistance * progress));
            
            // Gradually increase height
            intermediate = new Vec3(intermediate.x, start.y + (heightDiff * progress), intermediate.z);
            
            // Find a valid position near this intermediate point
            BlockPos validPos = findNearestValidPosition(new BlockPos((int)intermediate.x, (int)intermediate.y, (int)intermediate.z));
            if (validPos != null) {
                waypoints.add(Vec3.atCenterOf(validPos));
            }
        }
        
        waypoints.add(end);
        
        if (waypoints.size() >= 3) { // At least start, one intermediate, and end
            double estimatedTime = totalDistance / 0.25; // Slower movement on elevated paths
            return new PathfindingResult(PathType.ELEVATED, waypoints, null, estimatedTime, 4.0, false, 0);
        }
        
        return new PathfindingResult(PathType.IMPOSSIBLE, null, null, 0, 0, false, 0);
    }

    private PathfindingResult calculateBuildingPath(Vec3 start, Vec3 end) {
        if (!WardenAIConfig.enableBlockPlacement || !warden.canPlaceBlocks()) {
            return new PathfindingResult(PathType.IMPOSSIBLE, null, null, 0, 0, false, 0);
        }
        
        List<Vec3> waypoints = new ArrayList<>();
        List<BlockPos> requiredBlocks = new ArrayList<>();
        
        waypoints.add(start);
        
        // Calculate building positions
        Vec3 direction = end.subtract(start).normalize();
        double distance = start.distanceTo(end);
        double heightDiff = end.y - start.y;
        
        // If target is elevated, build a ramp or stairs
        if (heightDiff > 1.0) {
            int blocksNeeded = Math.min((int)Math.ceil(heightDiff), WardenAIConfig.maxBlocksPlaced);
            double stepDistance = distance / blocksNeeded;
            
            for (int i = 1; i <= blocksNeeded; i++) {
                Vec3 buildPos = start.add(direction.scale(stepDistance * i));
                buildPos = new Vec3(buildPos.x, start.y + (heightDiff * i / blocksNeeded), buildPos.z);
                
                BlockPos blockPos = new BlockPos((int)buildPos.x, (int)buildPos.y - 1, (int)buildPos.z);
                
                if (level.getBlockState(blockPos).isAir()) {
                    requiredBlocks.add(blockPos);
                    waypoints.add(Vec3.atCenterOf(blockPos.above()));
                }
            }
        }
        
        waypoints.add(end);
        
        if (!requiredBlocks.isEmpty() && requiredBlocks.size() <= WardenAIConfig.maxBlocksPlaced) {
            double estimatedTime = distance / 0.2 + (requiredBlocks.size() * 2); // Account for building time
            return new PathfindingResult(PathType.BUILDING_REQUIRED, waypoints, requiredBlocks, estimatedTime, 6.0, true, 0);
        }
        
        return new PathfindingResult(PathType.IMPOSSIBLE, null, null, 0, 0, false, 0);
    }

    private PathfindingResult selectOptimalPath(List<PathfindingResult> alternatives) {
        PathfindingResult best = null;
        double bestScore = Double.NEGATIVE_INFINITY;
        
        for (PathfindingResult result : alternatives) {
            if (result.pathType == PathType.IMPOSSIBLE) {
                continue;
            }
            
            // Score based on multiple factors
            double score = 0;
            
            // Prefer faster paths
            score += 100.0 / Math.max(1.0, result.estimatedTime);
            
            // Prefer simpler paths (lower difficulty)
            score += 50.0 / Math.max(1.0, result.difficulty);
            
            // Penalize building paths slightly
            if (result.requiresBuilding) {
                score -= 10.0;
            }
            
            // Prefer direct paths
            if (result.pathType == PathType.DIRECT) {
                score += 20.0;
            }
            
            if (score > bestScore) {
                bestScore = score;
                best = result;
            }
        }
        
        return best;
    }

    private void applyPathfindingResult(PathfindingResult result) {
        if (result == null || result.waypoints.isEmpty()) {
            return;
        }
        
        // Set navigation target to the next waypoint
        Vec3 nextWaypoint = result.waypoints.get(Math.min(1, result.waypoints.size() - 1));
        warden.getNavigation().moveTo(nextWaypoint.x, nextWaypoint.y, nextWaypoint.z, 1.0);
        
        // If building is required, signal the building goal
        if (result.requiresBuilding && !result.requiredBlocks.isEmpty()) {
            warden.setBuildingMode(true);
        }
    }

    private boolean isPathClear(Vec3 start, Vec3 end) {
        // Simple line-of-sight check
        Vec3 direction = end.subtract(start).normalize();
        double distance = start.distanceTo(end);
        int steps = (int)Math.ceil(distance);
        
        for (int i = 0; i <= steps; i++) {
            Vec3 checkPos = start.add(direction.scale(i));
            BlockPos blockPos = new BlockPos((int)checkPos.x, (int)checkPos.y, (int)checkPos.z);
            
            if (!level.getBlockState(blockPos).isAir()) {
                return false;
            }
        }
        
        return true;
    }

    private BlockPos findNearestValidPosition(BlockPos center) {
        for (int y = -2; y <= 2; y++) {
            for (int x = -2; x <= 2; x++) {
                for (int z = -2; z <= 2; z++) {
                    BlockPos pos = center.offset(x, y, z);
                    if (level.getBlockState(pos).isAir() && !level.getBlockState(pos.below()).isAir()) {
                        return pos;
                    }
                }
            }
        }
        return null;
    }

    private String getCacheKey(Vec3 start, Vec3 end) {
        return String.format("%.1f,%.1f,%.1f-%.1f,%.1f,%.1f", 
            start.x, start.y, start.z, end.x, end.y, end.z);
    }

    private void cacheResult(String key, PathfindingResult result) {
        if (pathCache.size() >= maxCacheSize) {
            // Remove oldest entry
            String oldestKey = pathCache.keySet().iterator().next();
            pathCache.remove(oldestKey);
        }
        pathCache.put(key, result);
    }

    // Getters
    public PathfindingResult getLastResult() {
        return lastResult;
    }

    public boolean hasValidPath() {
        return lastResult != null && lastResult.pathType != PathType.IMPOSSIBLE;
    }
}

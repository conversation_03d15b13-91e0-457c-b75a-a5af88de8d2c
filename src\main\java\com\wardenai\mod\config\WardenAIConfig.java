package com.wardenai.mod.config;

import com.wardenai.mod.WardenAIMod;
import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.config.ModConfigEvent;

/**
 * Configuration class for Warden AI Enhanced mod
 * 
 * Provides comprehensive configuration options for server administrators
 * to adjust AI behavior intensity, performance settings, and features.
 */
@Mod.EventBusSubscriber(modid = WardenAIMod.MODID, bus = Mod.EventBusSubscriber.Bus.MOD)
public class WardenAIConfig {
    
    private static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();

    // === AI Behavior Settings ===
    private static final ForgeConfigSpec.IntValue AI_BEHAVIOR_INTENSITY = BUILDER
            .comment("AI behavior intensity level (1-10). Higher values make the Warden more intelligent but use more resources.",
                     "1 = Basic AI, 5 = Balanced, 10 = Maximum intelligence")
            .defineInRange("aiBehaviorIntensity", 5, 1, 10);

    private static final ForgeConfigSpec.DoubleValue AI_DECISION_FREQUENCY = BUILDER
            .comment("How often the AI makes decisions (in seconds). Lower values = more responsive but higher CPU usage.")
            .defineInRange("aiDecisionFrequency", 0.5, 0.1, 2.0);

    private static final ForgeConfigSpec.IntValue PATHFINDING_RANGE = BUILDER
            .comment("Maximum range for advanced pathfinding calculations (in blocks)")
            .defineInRange("pathfindingRange", 32, 16, 64);

    // === Block Placement Settings ===
    private static final ForgeConfigSpec.BooleanValue ENABLE_BLOCK_PLACEMENT = BUILDER
            .comment("Enable intelligent block placement for reaching players at different elevations")
            .define("enableBlockPlacement", true);

    private static final ForgeConfigSpec.IntValue MAX_BLOCKS_PLACED = BUILDER
            .comment("Maximum number of blocks the Warden can place in a single structure")
            .defineInRange("maxBlocksPlaced", 20, 5, 50);

    private static final ForgeConfigSpec.IntValue BLOCK_PLACEMENT_COOLDOWN = BUILDER
            .comment("Cooldown between block placements (in ticks). 20 ticks = 1 second")
            .defineInRange("blockPlacementCooldown", 10, 5, 60);

    private static final ForgeConfigSpec.BooleanValue AUTO_CLEANUP_BLOCKS = BUILDER
            .comment("Automatically remove placed blocks after encounters to minimize world impact")
            .define("autoCleanupBlocks", true);

    private static final ForgeConfigSpec.IntValue CLEANUP_DELAY = BUILDER
            .comment("Delay before cleaning up placed blocks (in seconds)")
            .defineInRange("cleanupDelay", 30, 10, 300);

    // === Visual Accessibility Settings ===
    private static final ForgeConfigSpec.BooleanValue DISABLE_BLINDNESS_EFFECT = BUILDER
            .comment("Disable the blindness effect applied to players by the Warden")
            .define("disableBlindnessEffect", true);

    private static final ForgeConfigSpec.BooleanValue ENHANCED_VISIBILITY_INDICATORS = BUILDER
            .comment("Add visual indicators to help players track the enhanced Warden")
            .define("enhancedVisibilityIndicators", true);

    // === Performance Settings ===
    private static final ForgeConfigSpec.IntValue MAX_AI_CALCULATIONS_PER_TICK = BUILDER
            .comment("Maximum AI calculations per tick to prevent lag")
            .defineInRange("maxAiCalculationsPerTick", 10, 5, 50);

    private static final ForgeConfigSpec.BooleanValue ENABLE_PERFORMANCE_MONITORING = BUILDER
            .comment("Enable performance monitoring and automatic AI throttling")
            .define("enablePerformanceMonitoring", true);

    private static final ForgeConfigSpec.DoubleValue PERFORMANCE_THRESHOLD = BUILDER
            .comment("Performance threshold (ms per tick). AI will throttle if exceeded.")
            .defineInRange("performanceThreshold", 5.0, 1.0, 20.0);

    // === Combat Settings ===
    private static final ForgeConfigSpec.DoubleValue DAMAGE_MULTIPLIER = BUILDER
            .comment("Damage multiplier for the enhanced Warden (1.0 = vanilla damage)")
            .defineInRange("damageMultiplier", 1.0, 0.5, 2.0);

    private static final ForgeConfigSpec.DoubleValue HEALTH_MULTIPLIER = BUILDER
            .comment("Health multiplier for the enhanced Warden (1.0 = vanilla health)")
            .defineInRange("healthMultiplier", 1.0, 0.5, 2.0);

    private static final ForgeConfigSpec.DoubleValue SPEED_MULTIPLIER = BUILDER
            .comment("Speed multiplier for the enhanced Warden (1.0 = vanilla speed)")
            .defineInRange("speedMultiplier", 1.0, 0.5, 2.0);

    // === Debug Settings ===
    private static final ForgeConfigSpec.BooleanValue DEBUG_MODE = BUILDER
            .comment("Enable debug mode for AI behavior logging")
            .define("debugMode", true);

    private static final ForgeConfigSpec.BooleanValue SHOW_PATHFINDING_PARTICLES = BUILDER
            .comment("Show particles for pathfinding visualization (debug only)")
            .define("showPathfindingParticles", false);

    public static final ForgeConfigSpec SPEC = BUILDER.build();

    // Public static fields for easy access
    public static int aiBehaviorIntensity;
    public static double aiDecisionFrequency;
    public static int pathfindingRange;
    public static boolean enableBlockPlacement;
    public static int maxBlocksPlaced;
    public static int blockPlacementCooldown;
    public static boolean autoCleanupBlocks;
    public static int cleanupDelay;
    public static boolean disableBlindnessEffect;
    public static boolean enhancedVisibilityIndicators;
    public static int maxAiCalculationsPerTick;
    public static boolean enablePerformanceMonitoring;
    public static double performanceThreshold;
    public static double damageMultiplier;
    public static double healthMultiplier;
    public static double speedMultiplier;
    public static boolean debugMode;
    public static boolean showPathfindingParticles;

    @SubscribeEvent
    static void onLoad(final ModConfigEvent event) {
        // Load configuration values
        aiBehaviorIntensity = AI_BEHAVIOR_INTENSITY.get();
        aiDecisionFrequency = AI_DECISION_FREQUENCY.get();
        pathfindingRange = PATHFINDING_RANGE.get();
        enableBlockPlacement = ENABLE_BLOCK_PLACEMENT.get();
        maxBlocksPlaced = MAX_BLOCKS_PLACED.get();
        blockPlacementCooldown = BLOCK_PLACEMENT_COOLDOWN.get();
        autoCleanupBlocks = AUTO_CLEANUP_BLOCKS.get();
        cleanupDelay = CLEANUP_DELAY.get();
        disableBlindnessEffect = DISABLE_BLINDNESS_EFFECT.get();
        enhancedVisibilityIndicators = ENHANCED_VISIBILITY_INDICATORS.get();
        maxAiCalculationsPerTick = MAX_AI_CALCULATIONS_PER_TICK.get();
        enablePerformanceMonitoring = ENABLE_PERFORMANCE_MONITORING.get();
        performanceThreshold = PERFORMANCE_THRESHOLD.get();
        damageMultiplier = DAMAGE_MULTIPLIER.get();
        healthMultiplier = HEALTH_MULTIPLIER.get();
        speedMultiplier = SPEED_MULTIPLIER.get();
        debugMode = DEBUG_MODE.get();
        showPathfindingParticles = SHOW_PATHFINDING_PARTICLES.get();
    }
}

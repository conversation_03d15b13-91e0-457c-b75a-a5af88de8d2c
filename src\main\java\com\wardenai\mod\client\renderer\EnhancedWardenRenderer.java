package com.wardenai.mod.client.renderer;

import com.wardenai.mod.WardenAIMod;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import net.minecraft.client.renderer.entity.EntityRendererProvider;
import net.minecraft.client.renderer.entity.MobRenderer;
import net.minecraft.client.renderer.entity.layers.WardenEmissiveLayer;
import net.minecraft.client.model.WardenModel;
import net.minecraft.client.model.geom.ModelLayers;
import net.minecraft.resources.ResourceLocation;

/**
 * Renderer for the Enhanced Warden Entity
 * 
 * Uses the vanilla Warden model and textures but can be extended
 * for custom visual effects and enhancements.
 */
public class EnhancedWardenRenderer extends <PERSON>b<PERSON><PERSON>er<EnhancedWardenEntity, WardenModel<EnhancedWardenEntity>> {
    
    private static final ResourceLocation WARDEN_LOCATION = ResourceLocation.withDefaultNamespace("textures/entity/warden/warden.png");
    private static final ResourceLocation WARDEN_BIOLUMINESCENT_LAYER_LOCATION = ResourceLocation.withDefaultNamespace("textures/entity/warden/warden_bioluminescent_layer.png");
    private static final ResourceLocation WARDEN_HEART_LOCATION = ResourceLocation.withDefaultNamespace("textures/entity/warden/warden_heart.png");
    private static final ResourceLocation WARDEN_PULSATING_SPOTS_1_LOCATION = ResourceLocation.withDefaultNamespace("textures/entity/warden/warden_pulsating_spots_1.png");
    private static final ResourceLocation WARDEN_PULSATING_SPOTS_2_LOCATION = ResourceLocation.withDefaultNamespace("textures/entity/warden/warden_pulsating_spots_2.png");

    public EnhancedWardenRenderer(EntityRendererProvider.Context context) {
        super(context, new WardenModel<>(context.bakeLayer(ModelLayers.WARDEN)), 0.9F);
        
        // Add the emissive layer for the glowing effects
        this.addLayer(new WardenEmissiveLayer<>(this, WARDEN_BIOLUMINESCENT_LAYER_LOCATION, 
            (warden, partialTicks, ageInTicks) -> Math.max(0.0F, (float)Math.cos(ageInTicks * 0.045F) * 0.25F + 0.75F), 
            WardenModel::getBioluminescentLayerModelParts));
            
        this.addLayer(new WardenEmissiveLayer<>(this, WARDEN_PULSATING_SPOTS_1_LOCATION, 
            (warden, partialTicks, ageInTicks) -> Math.max(0.0F, (float)Math.cos(ageInTicks * 0.045F + 1.0F) * 0.25F + 0.75F), 
            WardenModel::getPulsatingSpotsLayerModelParts));
            
        this.addLayer(new WardenEmissiveLayer<>(this, WARDEN_PULSATING_SPOTS_2_LOCATION, 
            (warden, partialTicks, ageInTicks) -> Math.max(0.0F, (float)Math.cos(ageInTicks * 0.045F + 2.0F) * 0.25F + 0.75F), 
            WardenModel::getPulsatingSpotsLayerModelParts));
            
        this.addLayer(new WardenEmissiveLayer<>(this, WARDEN_HEART_LOCATION, 
            (warden, partialTicks, ageInTicks) -> {
                // Enhanced heart glow for AI Warden
                float heartBeat = (float)Math.cos(ageInTicks * 0.1F) * 0.3F + 0.7F;
                return Math.max(0.0F, heartBeat);
            }, 
            WardenModel::getHeartLayerModelParts));
    }

    @Override
    public ResourceLocation getTextureLocation(EnhancedWardenEntity entity) {
        return WARDEN_LOCATION;
    }

    @Override
    protected boolean isShaking(EnhancedWardenEntity entity) {
        // Add subtle shaking when the AI is in building mode or high intensity
        return entity.isInBuildingMode() || entity.getAIIntensity() >= 8;
    }

    @Override
    protected float getFlipDegrees(EnhancedWardenEntity entity) {
        return 0.0F; // Enhanced Wardens don't flip when dead
    }
}

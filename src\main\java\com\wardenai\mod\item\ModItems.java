package com.wardenai.mod.item;

import com.wardenai.mod.WardenAIMod;
import com.wardenai.mod.entity.ModEntities;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.SpawnEggItem;
import net.minecraftforge.common.ForgeSpawnEggItem;
import net.minecraftforge.registries.RegistryObject;

/**
 * Item registration for Warden AI Enhanced mod
 */
public class ModItems {
    
    // Enhanced Warden spawn egg
    public static final RegistryObject<SpawnEggItem> ENHANCED_WARDEN_SPAWN_EGG = 
        WardenAIMod.ITEMS.register("enhanced_warden_spawn_egg",
            () -> new ForgeSpawnEggItem(ModEntities.ENHANCED_WARDEN, 0x0F1419, 0x3C5F5F,
                new Item.Properties()));

    /**
     * Initialize item registrations
     */
    public static void init() {
        // Item registration is handled by the DeferredRegister
    }
}

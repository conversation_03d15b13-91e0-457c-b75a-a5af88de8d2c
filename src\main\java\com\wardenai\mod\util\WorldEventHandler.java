package com.wardenai.mod.util;

import com.wardenai.mod.WardenAIMod;
import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.Entity;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.event.entity.EntityJoinLevelEvent;
import net.minecraftforge.event.entity.EntityLeaveLevelEvent;
import net.minecraftforge.event.level.LevelEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * World Event Handler for Enhanced Warden AI
 * 
 * Manages world-level events including:
 * - Performance monitoring across all Enhanced Wardens
 * - Block cleanup management
 * - Entity tracking and lifecycle management
 * - Performance optimization and throttling
 */
@Mod.EventBusSubscriber(modid = WardenAIMod.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE)
public class WorldEventHandler {
    
    // Performance tracking
    private static final Map<UUID, PerformanceMonitor> wardenPerformanceMonitors = new HashMap<>();
    private static long lastGlobalPerformanceCheck = 0;
    private static final long GLOBAL_PERFORMANCE_CHECK_INTERVAL = 200; // 10 seconds
    
    // Global performance metrics
    private static double globalAverageTickTime = 0;
    private static int totalActiveWardens = 0;
    private static boolean globalThrottleActive = false;

    /**
     * Handles server tick events for performance monitoring and cleanup
     */
    @SubscribeEvent
    public static void onServerTick(TickEvent.ServerTickEvent event) {
        if (event.phase != TickEvent.Phase.END) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        
        // Process block cleanup for all server levels
        for (ServerLevel level : event.getServer().getAllLevels()) {
            BlockCleanupManager.getInstance().processCleanup(level);
        }
        
        // Global performance monitoring
        if (WardenAIConfig.enablePerformanceMonitoring && 
            currentTime - lastGlobalPerformanceCheck > GLOBAL_PERFORMANCE_CHECK_INTERVAL * 50) {
            performGlobalPerformanceCheck(event.getServer());
            lastGlobalPerformanceCheck = currentTime;
        }
    }

    /**
     * Handles Enhanced Warden entities joining the world
     */
    @SubscribeEvent
    public static void onEntityJoinLevel(EntityJoinLevelEvent event) {
        Entity entity = event.getEntity();
        
        if (entity instanceof EnhancedWardenEntity warden && !event.getLevel().isClientSide) {
            UUID wardenId = warden.getUUID();
            
            // Create performance monitor for this warden
            PerformanceMonitor monitor = new PerformanceMonitor();
            wardenPerformanceMonitors.put(wardenId, monitor);
            
            totalActiveWardens++;
            
            if (WardenAIConfig.debugMode) {
                System.out.println("Enhanced Warden joined world: " + wardenId + 
                                 " (Total active: " + totalActiveWardens + ")");
            }
        }
    }

    /**
     * Handles Enhanced Warden entities leaving the world
     */
    @SubscribeEvent
    public static void onEntityLeaveLevel(EntityLeaveLevelEvent event) {
        Entity entity = event.getEntity();
        
        if (entity instanceof EnhancedWardenEntity warden && !event.getLevel().isClientSide) {
            UUID wardenId = warden.getUUID();
            
            // Remove performance monitor
            wardenPerformanceMonitors.remove(wardenId);
            
            // Clean up any remaining blocks placed by this warden
            if (event.getLevel() instanceof ServerLevel serverLevel) {
                BlockCleanupManager.getInstance().cleanupWardenBlocks(serverLevel, wardenId);
            }
            
            totalActiveWardens = Math.max(0, totalActiveWardens - 1);
            
            if (WardenAIConfig.debugMode) {
                System.out.println("Enhanced Warden left world: " + wardenId + 
                                 " (Total active: " + totalActiveWardens + ")");
            }
        }
    }

    /**
     * Handles world unload events
     */
    @SubscribeEvent
    public static void onWorldUnload(LevelEvent.Unload event) {
        if (event.getLevel() instanceof ServerLevel) {
            // Clear all tracking data for this world
            BlockCleanupManager.getInstance().clearAll();
            
            if (WardenAIConfig.debugMode) {
                System.out.println("Cleared Enhanced Warden tracking data for unloaded world");
            }
        }
    }

    /**
     * Performs global performance analysis across all Enhanced Wardens
     */
    private static void performGlobalPerformanceCheck(net.minecraft.server.MinecraftServer server) {
        if (wardenPerformanceMonitors.isEmpty()) {
            return;
        }
        
        // Calculate global performance metrics
        double totalTickTime = 0;
        int throttledWardens = 0;
        int totalSamples = 0;
        
        for (PerformanceMonitor monitor : wardenPerformanceMonitors.values()) {
            PerformanceMonitor.PerformanceMetrics metrics = monitor.getMetrics();
            totalTickTime += metrics.averageTickTime * metrics.sampleCount;
            totalSamples += metrics.sampleCount;
            
            if (metrics.isThrottled) {
                throttledWardens++;
            }
        }
        
        globalAverageTickTime = totalSamples > 0 ? totalTickTime / totalSamples : 0;
        
        // Determine if global throttling is needed
        boolean shouldGlobalThrottle = (throttledWardens > totalActiveWardens / 2) || 
                                     (globalAverageTickTime > WardenAIConfig.performanceThreshold * 2);
        
        if (shouldGlobalThrottle != globalThrottleActive) {
            globalThrottleActive = shouldGlobalThrottle;
            
            if (WardenAIConfig.debugMode) {
                System.out.println("Global throttle " + (globalThrottleActive ? "activated" : "deactivated") + 
                                 " - Average tick time: " + String.format("%.2f", globalAverageTickTime) + "ms");
            }
        }
        
        // Log performance summary if debug mode is enabled
        if (WardenAIConfig.debugMode && totalActiveWardens > 0) {
            System.out.println(String.format(
                "Enhanced Warden Performance Summary: %d active, %.2fms avg tick time, %d throttled, global throttle: %s",
                totalActiveWardens, globalAverageTickTime, throttledWardens, globalThrottleActive
            ));
        }
        
        // Provide optimization recommendations if performance is poor
        if (globalAverageTickTime > WardenAIConfig.performanceThreshold * 1.5) {
            logPerformanceRecommendations();
        }
    }

    /**
     * Logs performance optimization recommendations
     */
    private static void logPerformanceRecommendations() {
        if (!WardenAIConfig.debugMode) {
            return;
        }
        
        System.out.println("=== Enhanced Warden Performance Recommendations ===");
        
        if (totalActiveWardens > 5) {
            System.out.println("- Consider reducing the number of Enhanced Wardens active simultaneously");
        }
        
        if (globalAverageTickTime > WardenAIConfig.performanceThreshold * 2) {
            System.out.println("- Reduce AI behavior intensity in configuration");
            System.out.println("- Increase AI decision frequency (make decisions less often)");
            System.out.println("- Consider disabling block placement if not essential");
        }
        
        System.out.println("- Current settings: AI Intensity=" + WardenAIConfig.aiBehaviorIntensity + 
                         ", Decision Frequency=" + WardenAIConfig.aiDecisionFrequency + "s");
        System.out.println("==================================================");
    }

    /**
     * Gets the performance monitor for a specific Enhanced Warden
     */
    public static PerformanceMonitor getWardenPerformanceMonitor(UUID wardenId) {
        return wardenPerformanceMonitors.get(wardenId);
    }

    /**
     * Gets global performance statistics
     */
    public static GlobalPerformanceStats getGlobalPerformanceStats() {
        return new GlobalPerformanceStats(
            totalActiveWardens,
            globalAverageTickTime,
            globalThrottleActive,
            wardenPerformanceMonitors.size(),
            BlockCleanupManager.getInstance().getStatistics()
        );
    }

    /**
     * Checks if global throttling is active
     */
    public static boolean isGlobalThrottleActive() {
        return globalThrottleActive;
    }

    /**
     * Forces a global performance check (for debugging)
     */
    public static void forceGlobalPerformanceCheck(net.minecraft.server.MinecraftServer server) {
        performGlobalPerformanceCheck(server);
    }

    /**
     * Resets all performance tracking data
     */
    public static void resetPerformanceTracking() {
        wardenPerformanceMonitors.clear();
        globalAverageTickTime = 0;
        totalActiveWardens = 0;
        globalThrottleActive = false;
        lastGlobalPerformanceCheck = 0;
    }

    public static class GlobalPerformanceStats {
        public final int activeWardens;
        public final double averageTickTime;
        public final boolean globalThrottleActive;
        public final int monitoredWardens;
        public final BlockCleanupManager.CleanupStatistics cleanupStats;

        public GlobalPerformanceStats(int active, double avgTime, boolean throttle, 
                                    int monitored, BlockCleanupManager.CleanupStatistics cleanup) {
            this.activeWardens = active;
            this.averageTickTime = avgTime;
            this.globalThrottleActive = throttle;
            this.monitoredWardens = monitored;
            this.cleanupStats = cleanup;
        }

        @Override
        public String toString() {
            return String.format(
                "GlobalPerformanceStats{active=%d, avgTime=%.2fms, throttle=%s, monitored=%d, cleanup=%s}",
                activeWardens, averageTickTime, globalThrottleActive, monitoredWardens, cleanupStats
            );
        }
    }

    /**
     * Gets performance recommendations based on current state
     */
    public static String[] getCurrentPerformanceRecommendations() {
        if (totalActiveWardens == 0) {
            return new String[]{"No Enhanced Wardens currently active"};
        }
        
        if (globalAverageTickTime < WardenAIConfig.performanceThreshold * 0.5) {
            return new String[]{
                "Performance is excellent",
                "Consider increasing AI intensity for better behavior",
                "All systems operating optimally"
            };
        } else if (globalAverageTickTime < WardenAIConfig.performanceThreshold) {
            return new String[]{
                "Performance is good",
                "Monitor during peak server load",
                "Current settings are well-balanced"
            };
        } else if (globalAverageTickTime < WardenAIConfig.performanceThreshold * 2) {
            return new String[]{
                "Performance is concerning",
                "Consider reducing AI behavior intensity",
                "Monitor block placement frequency",
                "Check server TPS during Warden encounters"
            };
        } else {
            return new String[]{
                "Performance is critical",
                "Immediately reduce AI behavior intensity",
                "Disable block placement temporarily",
                "Reduce number of active Enhanced Wardens",
                "Check server hardware resources"
            };
        }
    }
}

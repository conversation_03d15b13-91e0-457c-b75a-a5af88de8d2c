package com.wardenai.mod.util;

import com.wardenai.mod.config.WardenAIConfig;

import java.util.ArrayDeque;
import java.util.Deque;

/**
 * Performance Monitor for Enhanced Warden AI
 * 
 * Monitors AI performance metrics and provides automatic throttling
 * when performance thresholds are exceeded. Helps maintain server
 * stability while running advanced AI algorithms.
 */
public class PerformanceMonitor {
    
    // Performance tracking
    private final Deque<Long> tickTimes = new ArrayDeque<>();
    private final Deque<Integer> aiCalculations = new ArrayDeque<>();
    private final int maxSamples = 100; // Track last 100 ticks
    
    // Statistics
    private long totalTickTime = 0;
    private int totalCalculations = 0;
    private double averageTickTime = 0;
    private double averageCalculations = 0;
    
    // Throttling state
    private boolean isThrottled = false;
    private int throttleLevel = 0; // 0 = no throttle, 5 = maximum throttle
    private long lastThrottleTime = 0;
    private int consecutiveHighPerformanceTicks = 0;
    
    // Performance thresholds
    private static final double WARNING_THRESHOLD = 3.0; // 3ms per tick
    private static final double CRITICAL_THRESHOLD = 8.0; // 8ms per tick
    private static final int THROTTLE_RECOVERY_DELAY = 100; // 5 seconds in ticks

    public PerformanceMonitor() {
        // Initialize with default values
    }

    /**
     * Records the time taken for a tick
     * @param tickTimeMs Time in milliseconds
     */
    public void recordTickTime(long tickTimeMs) {
        // Add new sample
        tickTimes.addLast(tickTimeMs);
        totalTickTime += tickTimeMs;
        
        // Remove old samples if we exceed max
        if (tickTimes.size() > maxSamples) {
            long removed = tickTimes.removeFirst();
            totalTickTime -= removed;
        }
        
        // Update average
        averageTickTime = (double) totalTickTime / tickTimes.size();
        
        // Check for performance issues
        checkPerformanceThresholds(tickTimeMs);
    }

    /**
     * Records the number of AI calculations performed in a tick
     * @param calculations Number of calculations
     */
    public void recordAICalculations(int calculations) {
        aiCalculations.addLast(calculations);
        totalCalculations += calculations;
        
        if (aiCalculations.size() > maxSamples) {
            int removed = aiCalculations.removeFirst();
            totalCalculations -= removed;
        }
        
        averageCalculations = (double) totalCalculations / aiCalculations.size();
    }

    /**
     * Checks if performance thresholds are exceeded and adjusts throttling
     */
    private void checkPerformanceThresholds(long tickTimeMs) {
        if (!WardenAIConfig.enablePerformanceMonitoring) {
            return;
        }
        
        double threshold = WardenAIConfig.performanceThreshold;
        
        if (tickTimeMs > threshold) {
            consecutiveHighPerformanceTicks++;
            
            // Increase throttle level if we have consecutive high-performance ticks
            if (consecutiveHighPerformanceTicks >= 3 && throttleLevel < 5) {
                throttleLevel++;
                isThrottled = true;
                lastThrottleTime = System.currentTimeMillis();
                
                if (WardenAIConfig.debugMode) {
                    System.out.println("Performance Monitor: Increased throttle level to " + throttleLevel + 
                                     " (tick time: " + tickTimeMs + "ms)");
                }
            }
        } else {
            consecutiveHighPerformanceTicks = 0;
            
            // Gradually reduce throttling if performance is good
            if (isThrottled && System.currentTimeMillis() - lastThrottleTime > THROTTLE_RECOVERY_DELAY * 50) {
                throttleLevel = Math.max(0, throttleLevel - 1);
                if (throttleLevel == 0) {
                    isThrottled = false;
                }
                lastThrottleTime = System.currentTimeMillis();
                
                if (WardenAIConfig.debugMode) {
                    System.out.println("Performance Monitor: Reduced throttle level to " + throttleLevel);
                }
            }
        }
    }

    /**
     * Returns the current throttle multiplier for AI calculations
     * @return Multiplier between 0.2 and 1.0
     */
    public double getThrottleMultiplier() {
        if (!isThrottled) {
            return 1.0;
        }
        
        // Throttle multiplier decreases with throttle level
        return Math.max(0.2, 1.0 - (throttleLevel * 0.15));
    }

    /**
     * Returns the maximum number of AI calculations allowed this tick
     * @param baseLimit Base limit from configuration
     * @return Adjusted limit based on performance
     */
    public int getAdjustedCalculationLimit(int baseLimit) {
        return (int) (baseLimit * getThrottleMultiplier());
    }

    /**
     * Checks if AI should be throttled this tick
     * @return true if AI should reduce activity
     */
    public boolean shouldThrottleAI() {
        return isThrottled && throttleLevel >= 2;
    }

    /**
     * Gets the current performance status
     * @return Performance status string
     */
    public PerformanceStatus getPerformanceStatus() {
        if (averageTickTime < WARNING_THRESHOLD) {
            return PerformanceStatus.GOOD;
        } else if (averageTickTime < CRITICAL_THRESHOLD) {
            return PerformanceStatus.WARNING;
        } else {
            return PerformanceStatus.CRITICAL;
        }
    }

    /**
     * Gets detailed performance metrics
     * @return Performance metrics object
     */
    public PerformanceMetrics getMetrics() {
        return new PerformanceMetrics(
            averageTickTime,
            averageCalculations,
            tickTimes.size(),
            isThrottled,
            throttleLevel,
            getPerformanceStatus()
        );
    }

    /**
     * Resets all performance tracking data
     */
    public void reset() {
        tickTimes.clear();
        aiCalculations.clear();
        totalTickTime = 0;
        totalCalculations = 0;
        averageTickTime = 0;
        averageCalculations = 0;
        isThrottled = false;
        throttleLevel = 0;
        consecutiveHighPerformanceTicks = 0;
    }

    /**
     * Gets the recent tick time trend
     * @return Trend direction
     */
    public PerformanceTrend getTickTimeTrend() {
        if (tickTimes.size() < 10) {
            return PerformanceTrend.STABLE;
        }
        
        // Compare recent average with older average
        long recentSum = 0;
        long olderSum = 0;
        int halfSize = tickTimes.size() / 2;
        
        Long[] times = tickTimes.toArray(new Long[0]);
        
        for (int i = 0; i < halfSize; i++) {
            olderSum += times[i];
        }
        
        for (int i = halfSize; i < times.length; i++) {
            recentSum += times[i];
        }
        
        double recentAvg = (double) recentSum / halfSize;
        double olderAvg = (double) olderSum / halfSize;
        
        double difference = recentAvg - olderAvg;
        
        if (Math.abs(difference) < 0.5) {
            return PerformanceTrend.STABLE;
        } else if (difference > 0) {
            return PerformanceTrend.DEGRADING;
        } else {
            return PerformanceTrend.IMPROVING;
        }
    }

    public enum PerformanceStatus {
        GOOD,
        WARNING,
        CRITICAL
    }

    public enum PerformanceTrend {
        IMPROVING,
        STABLE,
        DEGRADING
    }

    public static class PerformanceMetrics {
        public final double averageTickTime;
        public final double averageCalculations;
        public final int sampleCount;
        public final boolean isThrottled;
        public final int throttleLevel;
        public final PerformanceStatus status;

        public PerformanceMetrics(double avgTickTime, double avgCalc, int samples, 
                                boolean throttled, int throttleLvl, PerformanceStatus stat) {
            this.averageTickTime = avgTickTime;
            this.averageCalculations = avgCalc;
            this.sampleCount = samples;
            this.isThrottled = throttled;
            this.throttleLevel = throttleLvl;
            this.status = stat;
        }

        @Override
        public String toString() {
            return String.format("PerformanceMetrics{avgTickTime=%.2fms, avgCalc=%.1f, samples=%d, throttled=%s, level=%d, status=%s}",
                    averageTickTime, averageCalculations, sampleCount, isThrottled, throttleLevel, status);
        }
    }

    // Getters for individual metrics
    public double getAverageTickTime() {
        return averageTickTime;
    }

    public double getAverageCalculations() {
        return averageCalculations;
    }

    public boolean isThrottled() {
        return isThrottled;
    }

    public int getThrottleLevel() {
        return throttleLevel;
    }

    public int getSampleCount() {
        return tickTimes.size();
    }

    /**
     * Provides recommendations for performance optimization
     * @return Array of recommendation strings
     */
    public String[] getOptimizationRecommendations() {
        PerformanceStatus status = getPerformanceStatus();
        
        switch (status) {
            case CRITICAL:
                return new String[]{
                    "Reduce AI behavior intensity in config",
                    "Increase AI decision frequency (less frequent decisions)",
                    "Disable block placement if not essential",
                    "Reduce pathfinding range",
                    "Enable performance monitoring if disabled"
                };
            case WARNING:
                return new String[]{
                    "Consider reducing AI behavior intensity",
                    "Monitor server performance during peak hours",
                    "Adjust performance threshold if needed"
                };
            case GOOD:
            default:
                return new String[]{
                    "Performance is optimal",
                    "Consider increasing AI intensity for better behavior"
                };
        }
    }
}

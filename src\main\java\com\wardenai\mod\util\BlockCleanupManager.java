package com.wardenai.mod.util;

import com.wardenai.mod.config.WardenAIConfig;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Block Cleanup Manager for Enhanced Warden
 * 
 * Manages the cleanup of blocks placed by Enhanced Wardens to minimize
 * world impact. Tracks placed blocks with timestamps and removes them
 * after a configurable delay. Includes safety checks to prevent
 * removing important structures.
 */
public class BlockCleanupManager {
    
    // Singleton instance
    private static BlockCleanupManager instance;
    
    // Tracking placed blocks with timestamps
    private final Map<BlockPos, PlacedBlockInfo> placedBlocks = new ConcurrentHashMap<>();
    private final Map<UUID, Set<BlockPos>> wardenBlocks = new ConcurrentHashMap<>();
    
    // Cleanup scheduling
    private final Queue<BlockPos> cleanupQueue = new LinkedList<>();
    private long lastCleanupCheck = 0;
    private static final long CLEANUP_CHECK_INTERVAL = 100; // 5 seconds in ticks
    
    // Safety settings
    private static final Set<BlockState> SAFE_TO_REMOVE_BLOCKS = Set.of(
        Blocks.COBBLESTONE.defaultBlockState(),
        Blocks.STONE.defaultBlockState(),
        Blocks.DIRT.defaultBlockState(),
        Blocks.DEEPSLATE.defaultBlockState(),
        Blocks.SCAFFOLDING.defaultBlockState()
    );

    public static class PlacedBlockInfo {
        public final BlockPos position;
        public final BlockState originalState;
        public final BlockState placedState;
        public final long placementTime;
        public final UUID wardenId;
        public final String placementReason;
        public boolean markedForCleanup;

        public PlacedBlockInfo(BlockPos pos, BlockState original, BlockState placed, 
                             UUID warden, String reason) {
            this.position = pos;
            this.originalState = original;
            this.placedState = placed;
            this.placementTime = System.currentTimeMillis();
            this.wardenId = warden;
            this.placementReason = reason;
            this.markedForCleanup = false;
        }

        public boolean shouldCleanup() {
            if (!WardenAIConfig.autoCleanupBlocks) {
                return false;
            }
            
            long age = System.currentTimeMillis() - placementTime;
            long cleanupDelay = WardenAIConfig.cleanupDelay * 1000L; // Convert to milliseconds
            
            return age >= cleanupDelay;
        }
    }

    private BlockCleanupManager() {
        // Private constructor for singleton
    }

    public static BlockCleanupManager getInstance() {
        if (instance == null) {
            instance = new BlockCleanupManager();
        }
        return instance;
    }

    /**
     * Registers a block placement for cleanup tracking
     */
    public void registerBlockPlacement(BlockPos pos, BlockState originalState, 
                                     BlockState placedState, UUID wardenId, String reason) {
        PlacedBlockInfo info = new PlacedBlockInfo(pos, originalState, placedState, wardenId, reason);
        placedBlocks.put(pos, info);
        
        // Track blocks by warden
        wardenBlocks.computeIfAbsent(wardenId, k -> ConcurrentHashMap.newKeySet()).add(pos);
        
        if (WardenAIConfig.debugMode) {
            System.out.println("Registered block placement at " + pos + " by warden " + wardenId + 
                             " (reason: " + reason + ")");
        }
    }

    /**
     * Processes cleanup operations - should be called regularly
     */
    public void processCleanup(ServerLevel level) {
        long currentTime = level.getGameTime();
        
        // Only check for cleanup periodically
        if (currentTime - lastCleanupCheck < CLEANUP_CHECK_INTERVAL) {
            return;
        }
        
        lastCleanupCheck = currentTime;
        
        // Check all placed blocks for cleanup eligibility
        Iterator<Map.Entry<BlockPos, PlacedBlockInfo>> iterator = placedBlocks.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<BlockPos, PlacedBlockInfo> entry = iterator.next();
            PlacedBlockInfo info = entry.getValue();
            
            if (info.shouldCleanup() && !info.markedForCleanup) {
                if (isSafeToCleanup(level, info)) {
                    cleanupQueue.offer(info.position);
                    info.markedForCleanup = true;
                }
            }
        }
        
        // Process cleanup queue (limit to prevent lag)
        int cleanupLimit = 5; // Clean up to 5 blocks per tick
        for (int i = 0; i < cleanupLimit && !cleanupQueue.isEmpty(); i++) {
            BlockPos pos = cleanupQueue.poll();
            if (pos != null) {
                performBlockCleanup(level, pos);
            }
        }
    }

    /**
     * Performs the actual block cleanup
     */
    private void performBlockCleanup(ServerLevel level, BlockPos pos) {
        PlacedBlockInfo info = placedBlocks.get(pos);
        if (info == null) {
            return;
        }
        
        // Double-check safety before removal
        if (!isSafeToCleanup(level, info)) {
            if (WardenAIConfig.debugMode) {
                System.out.println("Skipping cleanup of block at " + pos + " - not safe to remove");
            }
            return;
        }
        
        // Restore original block state
        BlockState currentState = level.getBlockState(pos);
        if (currentState.equals(info.placedState) || SAFE_TO_REMOVE_BLOCKS.contains(currentState)) {
            level.setBlock(pos, info.originalState, 3);
            
            if (WardenAIConfig.debugMode) {
                System.out.println("Cleaned up block at " + pos + " (restored to " + 
                                 info.originalState + ")");
            }
        }
        
        // Remove from tracking
        placedBlocks.remove(pos);
        if (info.wardenId != null) {
            Set<BlockPos> wardenBlockSet = wardenBlocks.get(info.wardenId);
            if (wardenBlockSet != null) {
                wardenBlockSet.remove(pos);
                if (wardenBlockSet.isEmpty()) {
                    wardenBlocks.remove(info.wardenId);
                }
            }
        }
    }

    /**
     * Checks if it's safe to cleanup a block
     */
    private boolean isSafeToCleanup(ServerLevel level, PlacedBlockInfo info) {
        BlockPos pos = info.position;
        BlockState currentState = level.getBlockState(pos);
        
        // Don't remove if the block has been modified by players or other systems
        if (!currentState.equals(info.placedState) && !SAFE_TO_REMOVE_BLOCKS.contains(currentState)) {
            return false;
        }
        
        // Don't remove if there are entities standing on the block
        if (hasEntitiesNearby(level, pos)) {
            return false;
        }
        
        // Don't remove if the block is supporting other important structures
        if (isSupportingStructure(level, pos)) {
            return false;
        }
        
        // Don't remove if players are nearby and might be using the structure
        if (hasPlayersNearby(level, pos, 8.0)) {
            return false;
        }
        
        return true;
    }

    /**
     * Checks if there are entities nearby that might be affected by block removal
     */
    private boolean hasEntitiesNearby(ServerLevel level, BlockPos pos) {
        return !level.getEntitiesOfClass(net.minecraft.world.entity.Entity.class, 
            new net.minecraft.world.phys.AABB(pos).inflate(2.0)).isEmpty();
    }

    /**
     * Checks if the block is supporting other structures
     */
    private boolean isSupportingStructure(ServerLevel level, BlockPos pos) {
        // Check blocks above for non-air blocks that might fall
        for (int y = 1; y <= 3; y++) {
            BlockPos above = pos.above(y);
            BlockState aboveState = level.getBlockState(above);
            
            if (!aboveState.isAir() && !SAFE_TO_REMOVE_BLOCKS.contains(aboveState)) {
                // Check if this block would fall without support
                if (wouldFallWithoutSupport(level, above)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Checks if a block would fall without the support block
     */
    private boolean wouldFallWithoutSupport(ServerLevel level, BlockPos pos) {
        BlockState state = level.getBlockState(pos);
        
        // Simple check for gravity-affected blocks
        return state.getBlock() == Blocks.SAND || 
               state.getBlock() == Blocks.GRAVEL ||
               state.getBlock() == Blocks.RED_SAND;
    }

    /**
     * Checks if there are players nearby
     */
    private boolean hasPlayersNearby(ServerLevel level, BlockPos pos, double range) {
        return level.players().stream()
            .anyMatch(player -> player.distanceToSqr(pos.getX(), pos.getY(), pos.getZ()) <= range * range);
    }

    /**
     * Forces cleanup of all blocks placed by a specific warden
     */
    public void cleanupWardenBlocks(ServerLevel level, UUID wardenId) {
        Set<BlockPos> wardenBlockSet = wardenBlocks.get(wardenId);
        if (wardenBlockSet == null) {
            return;
        }
        
        for (BlockPos pos : new HashSet<>(wardenBlockSet)) {
            PlacedBlockInfo info = placedBlocks.get(pos);
            if (info != null && isSafeToCleanup(level, info)) {
                performBlockCleanup(level, pos);
            }
        }
    }

    /**
     * Forces cleanup of all tracked blocks (emergency cleanup)
     */
    public void forceCleanupAll(ServerLevel level) {
        for (BlockPos pos : new HashSet<>(placedBlocks.keySet())) {
            PlacedBlockInfo info = placedBlocks.get(pos);
            if (info != null) {
                performBlockCleanup(level, pos);
            }
        }
    }

    /**
     * Gets statistics about tracked blocks
     */
    public CleanupStatistics getStatistics() {
        int totalBlocks = placedBlocks.size();
        int markedForCleanup = (int) placedBlocks.values().stream()
            .mapToLong(info -> info.markedForCleanup ? 1 : 0)
            .sum();
        int queuedForCleanup = cleanupQueue.size();
        int activeWardens = wardenBlocks.size();
        
        return new CleanupStatistics(totalBlocks, markedForCleanup, queuedForCleanup, activeWardens);
    }

    public static class CleanupStatistics {
        public final int totalTrackedBlocks;
        public final int markedForCleanup;
        public final int queuedForCleanup;
        public final int activeWardens;

        public CleanupStatistics(int total, int marked, int queued, int wardens) {
            this.totalTrackedBlocks = total;
            this.markedForCleanup = marked;
            this.queuedForCleanup = queued;
            this.activeWardens = wardens;
        }

        @Override
        public String toString() {
            return String.format("CleanupStats{total=%d, marked=%d, queued=%d, wardens=%d}",
                    totalTrackedBlocks, markedForCleanup, queuedForCleanup, activeWardens);
        }
    }

    /**
     * Checks if a block position is tracked for cleanup
     */
    public boolean isBlockTracked(BlockPos pos) {
        return placedBlocks.containsKey(pos);
    }

    /**
     * Gets information about a tracked block
     */
    public PlacedBlockInfo getBlockInfo(BlockPos pos) {
        return placedBlocks.get(pos);
    }

    /**
     * Manually marks a block for immediate cleanup
     */
    public void markForImmediateCleanup(BlockPos pos) {
        PlacedBlockInfo info = placedBlocks.get(pos);
        if (info != null && !info.markedForCleanup) {
            info.markedForCleanup = true;
            cleanupQueue.offer(pos);
        }
    }

    /**
     * Removes a block from tracking without cleanup (if manually removed)
     */
    public void removeFromTracking(BlockPos pos) {
        PlacedBlockInfo info = placedBlocks.remove(pos);
        if (info != null && info.wardenId != null) {
            Set<BlockPos> wardenBlockSet = wardenBlocks.get(info.wardenId);
            if (wardenBlockSet != null) {
                wardenBlockSet.remove(pos);
                if (wardenBlockSet.isEmpty()) {
                    wardenBlocks.remove(info.wardenId);
                }
            }
        }
    }

    /**
     * Clears all tracking data (for world unload/reload)
     */
    public void clearAll() {
        placedBlocks.clear();
        wardenBlocks.clear();
        cleanupQueue.clear();
    }
}

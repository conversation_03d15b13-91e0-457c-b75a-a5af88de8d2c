package com.wardenai.mod.event;

import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.MobEffectEvent;
import net.minecraftforge.eventbus.api.Event;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * Event handler to intercept and cancel darkness/blindness effects from Enhanced Wardens
 * when accessibility features are enabled.
 */
@Mod.EventBusSubscriber(modid = "wardenai", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class WardenEffectHandler {

    @SubscribeEvent
    public static void onMobEffectApplicable(MobEffectEvent.Applicable event) {
        // Only process if accessibility is enabled
        if (!WardenAIConfig.disableBlindnessEffect) {
            return;
        }

        // Check if this is a darkness or blindness effect
        if (event.getEffectInstance().getEffect() != MobEffects.DARKNESS && 
            event.getEffectInstance().getEffect() != MobEffects.BLINDNESS) {
            return;
        }

        // Check if the target is a player
        if (!(event.getEntity() instanceof Player player)) {
            return;
        }

        // Check if there's an Enhanced Warden nearby that could be the source
        boolean hasEnhancedWardenNearby = player.level().getEntitiesOfClass(
            EnhancedWardenEntity.class, 
            player.getBoundingBox().inflate(32.0)
        ).stream().anyMatch(warden -> warden.getTarget() == player);

        if (hasEnhancedWardenNearby) {
            // Cancel the effect
            event.setResult(Event.Result.DENY);
            
            if (WardenAIConfig.debugMode) {
                System.out.println("WardenEffectHandler: Blocked " + 
                    event.getEffectInstance().getEffect().getDescriptionId() + 
                    " effect from Enhanced Warden for accessibility");
            }
        }
    }

    @SubscribeEvent
    public static void onMobEffectAdded(MobEffectEvent.Added event) {
        // Also try to catch effects that were already added
        if (!WardenAIConfig.disableBlindnessEffect) {
            return;
        }

        if (event.getEffectInstance().getEffect() != MobEffects.DARKNESS && 
            event.getEffectInstance().getEffect() != MobEffects.BLINDNESS) {
            return;
        }

        if (!(event.getEntity() instanceof Player player)) {
            return;
        }

        // Check if there's an Enhanced Warden nearby
        boolean hasEnhancedWardenNearby = player.level().getEntitiesOfClass(
            EnhancedWardenEntity.class, 
            player.getBoundingBox().inflate(32.0)
        ).stream().anyMatch(warden -> warden.getTarget() == player);

        if (hasEnhancedWardenNearby) {
            // Remove the effect immediately
            player.removeEffect(event.getEffectInstance().getEffect());
            
            if (WardenAIConfig.debugMode) {
                System.out.println("WardenEffectHandler: Removed " + 
                    event.getEffectInstance().getEffect().getDescriptionId() + 
                    " effect from Enhanced Warden for accessibility");
            }
        }
    }
}

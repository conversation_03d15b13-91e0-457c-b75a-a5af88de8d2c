package com.wardenai.mod.event;

import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.MobEffectEvent;
import net.minecraftforge.eventbus.api.Event;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * Comprehensive event handler to completely prevent darkness/blindness effects
 * from Enhanced Wardens when accessibility features are enabled.
 */
@Mod.EventBusSubscriber(modid = "wardenai", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class WardenEffectHandler {

    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onLivingAttack(LivingAttackEvent event) {
        // Intercept attacks from Enhanced Wardens to prevent any effects
        if (!(event.getEntity() instanceof Player player)) {
            return;
        }

        if (!(event.getSource().getEntity() instanceof EnhancedWardenEntity)) {
            return;
        }

        // Enhanced Wardens are accessibility-friendly - no visual impairment effects
        if (WardenAIConfig.debugMode) {
            System.out.println("WardenEffectHandler: Intercepted Enhanced Warden attack - will prevent all visual effects");
        }
    }

    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onMobEffectApplicable(MobEffectEvent.Applicable event) {
        // Block darkness/blindness effects completely
        if (event.getEffectInstance().getEffect() != MobEffects.DARKNESS &&
            event.getEffectInstance().getEffect() != MobEffects.BLINDNESS) {
            return;
        }

        // Check if the target is a player
        if (!(event.getEntity() instanceof Player player)) {
            return;
        }

        // Check if there's an Enhanced Warden nearby that could be the source
        boolean hasEnhancedWardenNearby = player.level().getEntitiesOfClass(
            EnhancedWardenEntity.class,
            player.getBoundingBox().inflate(32.0)
        ).stream().anyMatch(warden -> warden.getTarget() == player);

        if (hasEnhancedWardenNearby) {
            // COMPLETELY DENY the effect - it will never be applied
            event.setResult(Event.Result.DENY);

            if (WardenAIConfig.debugMode) {
                System.out.println("WardenEffectHandler: DENIED " +
                    event.getEffectInstance().getEffect().getDescriptionId() +
                    " effect from Enhanced Warden (accessibility mode)");
            }
        }
    }

    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onMobEffectAdded(MobEffectEvent.Added event) {
        // Backup: Remove any darkness/blindness effects that somehow got through
        if (event.getEffectInstance().getEffect() != MobEffects.DARKNESS &&
            event.getEffectInstance().getEffect() != MobEffects.BLINDNESS) {
            return;
        }

        if (!(event.getEntity() instanceof Player player)) {
            return;
        }

        // Check if there's an Enhanced Warden nearby
        boolean hasEnhancedWardenNearby = player.level().getEntitiesOfClass(
            EnhancedWardenEntity.class,
            player.getBoundingBox().inflate(32.0)
        ).stream().anyMatch(warden -> warden.getTarget() == player);

        if (hasEnhancedWardenNearby) {
            // Immediately remove the effect
            player.removeEffect(event.getEffectInstance().getEffect());

            if (WardenAIConfig.debugMode) {
                System.out.println("WardenEffectHandler: EMERGENCY REMOVAL of " +
                    event.getEffectInstance().getEffect().getDescriptionId() +
                    " effect from Enhanced Warden");
            }
        }
    }
}

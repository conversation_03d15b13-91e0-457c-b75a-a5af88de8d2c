package com.wardenai.mod.entity;

import com.wardenai.mod.WardenAIMod;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.MobCategory;
import net.minecraftforge.registries.RegistryObject;

/**
 * Entity registration class for Warden AI Enhanced mod
 */
public class ModEntities {
    
    // Enhanced Warden entity type
    public static final RegistryObject<EntityType<EnhancedWardenEntity>> ENHANCED_WARDEN = 
        WardenAIMod.ENTITY_TYPES.register("enhanced_warden", 
            () -> EntityType.Builder.of(EnhancedWardenEntity::new, MobCategory.MONSTER)
                .sized(0.9F, 2.9F) // Same size as vanilla Warden
                .clientTrackingRange(16)
                .updateInterval(2)
                .build("enhanced_warden"));

    /**
     * Initialize entity registrations
     * Called during common setup
     */
    public static void init() {
        // Entity registration is handled by the DeferredRegister
        // This method exists for explicit initialization if needed
    }
}

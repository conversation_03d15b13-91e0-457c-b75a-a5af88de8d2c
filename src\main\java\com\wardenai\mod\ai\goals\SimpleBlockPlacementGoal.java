package com.wardenai.mod.ai.goals;

import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

/**
 * SIMPLE building goal - just build one block at a time directly towards the target
 */
public class SimpleBlockPlacementGoal extends Goal {
    
    private final EnhancedWardenEntity warden;
    private LivingEntity target;
    private int buildCooldown = 0;
    private BlockPos lastPlacedBlock = null;
    
    public SimpleBlockPlacementGoal(EnhancedWardenEntity warden) {
        this.warden = warden;
    }
    
    @Override
    public boolean canUse() {
        if (!WardenAIConfig.enableBlockPlacement) {
            return false;
        }
        
        this.target = warden.getTarget();
        if (target == null) {
            return false;
        }
        
        // Simple check: is target higher than us?
        double heightDiff = target.getY() - warden.getY();
        double distance = warden.distanceTo(target);
        boolean targetHigher = heightDiff > 1.0; // More aggressive - build even for small height differences

        if (WardenAIConfig.debugMode) {
            System.out.println("SimpleBuilding.canUse(): heightDiff=" + String.format("%.1f", heightDiff) +
                             ", distance=" + String.format("%.1f", distance) +
                             ", targetHigher=" + targetHigher +
                             ", cooldown=" + buildCooldown);
        }

        return targetHigher && buildCooldown <= 0;
    }
    
    @Override
    public boolean canContinueToUse() {
        return canUse();
    }
    
    @Override
    public void start() {
        if (WardenAIConfig.debugMode) {
            System.out.println("SimpleBuilding: STARTED - target is elevated");
        }
    }
    
    @Override
    public void tick() {
        if (buildCooldown > 0) {
            buildCooldown--;
            if (WardenAIConfig.debugMode && buildCooldown % 10 == 0) {
                System.out.println("SimpleBuilding: Cooldown remaining: " + buildCooldown);
            }
            return;
        }

        if (target == null) {
            if (WardenAIConfig.debugMode) {
                System.out.println("SimpleBuilding: No target, stopping");
            }
            return;
        }

        BlockPos wardenPos = warden.blockPosition();
        BlockPos targetPos = target.blockPosition();

        if (WardenAIConfig.debugMode) {
            System.out.println("SimpleBuilding.tick(): Warden at " + wardenPos + ", Target at " + targetPos);
        }

        // Step 1: If we placed a block, move to it first
        if (lastPlacedBlock != null && !isWardenOnBlock(lastPlacedBlock)) {
            moveToBlock(lastPlacedBlock);
            if (WardenAIConfig.debugMode) {
                System.out.println("SimpleBuilding: Still moving to last placed block " + lastPlacedBlock);
            }
            return;
        }

        // Step 2: Find where to place the next block
        BlockPos nextBlockPos = findNextBlockPosition();
        if (nextBlockPos != null) {
            if (WardenAIConfig.debugMode) {
                System.out.println("SimpleBuilding: Attempting to place block at " + nextBlockPos);
            }

            if (placeBlock(nextBlockPos)) {
                lastPlacedBlock = nextBlockPos;
                buildCooldown = 15; // Reduced cooldown for faster building

                // Immediately start moving to the new block
                moveToBlock(nextBlockPos);

                if (WardenAIConfig.debugMode) {
                    System.out.println("SimpleBuilding: SUCCESS! Placed block at " + nextBlockPos + ", moving to it");
                }
            } else {
                if (WardenAIConfig.debugMode) {
                    System.out.println("SimpleBuilding: FAILED to place block at " + nextBlockPos);
                }
            }
        } else {
            if (WardenAIConfig.debugMode) {
                System.out.println("SimpleBuilding: No valid position found for next block");
            }
        }
    }
    
    private BlockPos findNextBlockPosition() {
        BlockPos wardenPos = warden.blockPosition();
        BlockPos targetPos = target.blockPosition();

        double heightDiff = target.getY() - warden.getY();

        if (WardenAIConfig.debugMode) {
            System.out.println("SimpleBuilding: Finding next block. Warden at " + wardenPos +
                             ", Target at " + targetPos + ", heightDiff=" + String.format("%.1f", heightDiff));
        }

        // ULTRA SIMPLE: Just build straight up first, then towards target
        BlockPos[] candidates = {
            // 1. Build straight up from current position (easiest)
            wardenPos.offset(0, 1, 0),
            // 2. Build one block towards target horizontally at current level
            wardenPos.offset(Integer.compare(targetPos.getX(), wardenPos.getX()), 0, 0),
            wardenPos.offset(0, 0, Integer.compare(targetPos.getZ(), wardenPos.getZ())),
            // 3. Build up and towards target (if we can)
            wardenPos.offset(Integer.compare(targetPos.getX(), wardenPos.getX()), 1, 0),
            wardenPos.offset(0, 1, Integer.compare(targetPos.getZ(), wardenPos.getZ()))
        };

        for (int i = 0; i < candidates.length; i++) {
            BlockPos candidate = candidates[i];
            if (isValidPlacePosition(candidate)) {
                if (WardenAIConfig.debugMode) {
                    System.out.println("SimpleBuilding: Selected position " + candidate + " (option " + (i+1) + ")");
                }
                return candidate;
            } else {
                if (WardenAIConfig.debugMode) {
                    System.out.println("SimpleBuilding: Position " + candidate + " invalid (option " + (i+1) + ")");
                }
            }
        }

        if (WardenAIConfig.debugMode) {
            System.out.println("SimpleBuilding: No valid positions found!");
        }
        return null;
    }
    
    private boolean isValidPlacePosition(BlockPos pos) {
        // Check if we can place a block here
        BlockState currentState = warden.level().getBlockState(pos);
        BlockState belowState = warden.level().getBlockState(pos.below());
        
        // Must be air or replaceable, and have solid ground below
        return (currentState.isAir() || currentState.canBeReplaced()) && 
               !belowState.isAir() && 
               belowState.isSolid();
    }
    
    private boolean placeBlock(BlockPos pos) {
        if (!isValidPlacePosition(pos)) {
            return false;
        }
        
        // Place cobblestone block
        BlockState blockToPlace = Blocks.COBBLESTONE.defaultBlockState();
        warden.level().setBlock(pos, blockToPlace, 3);
        
        // Add to warden's placed blocks list for cleanup
        warden.getPlacedBlocks().add(pos);
        
        // Play sound
        warden.level().playSound(null, pos, blockToPlace.getSoundType().getPlaceSound(),
                               warden.getSoundSource(), 1.0F, 1.0F);
        
        return true;
    }
    
    private void moveToBlock(BlockPos blockPos) {
        // Move to the center of the block, one block above it
        double x = blockPos.getX() + 0.5;
        double y = blockPos.getY() + 1.0;
        double z = blockPos.getZ() + 0.5;
        
        warden.getNavigation().moveTo(x, y, z, 1.2); // Fast movement
    }
    
    private boolean isWardenOnBlock(BlockPos blockPos) {
        BlockPos wardenPos = warden.blockPosition();
        // Check if warden is standing on or very close to the block
        return Math.abs(wardenPos.getX() - blockPos.getX()) <= 1 && 
               Math.abs(wardenPos.getZ() - blockPos.getZ()) <= 1 && 
               Math.abs(wardenPos.getY() - (blockPos.getY() + 1)) <= 1;
    }
    
    @Override
    public void stop() {
        lastPlacedBlock = null;
        if (WardenAIConfig.debugMode) {
            System.out.println("SimpleBuilding: STOPPED");
        }
    }
}

package com.wardenai.mod.ai.goals;

import com.wardenai.mod.config.WardenAIConfig;
import com.wardenai.mod.entity.EnhancedWardenEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

/**
 * SIMPLE building goal - just build one block at a time directly towards the target
 */
public class SimpleBlockPlacementGoal extends Goal {
    
    private final EnhancedWardenEntity warden;
    private LivingEntity target;
    private int buildCooldown = 0;
    private BlockPos lastPlacedBlock = null;
    
    public SimpleBlockPlacementGoal(EnhancedWardenEntity warden) {
        this.warden = warden;
    }
    
    @Override
    public boolean canUse() {
        if (!WardenAIConfig.enableBlockPlacement) {
            return false;
        }
        
        this.target = warden.getTarget();
        if (target == null) {
            return false;
        }
        
        // Simple check: is target higher than us?
        double heightDiff = target.getY() - warden.getY();
        boolean targetHigher = heightDiff > 1.5;
        
        if (WardenAIConfig.debugMode) {
            System.out.println("SimpleBuilding: heightDiff=" + String.format("%.1f", heightDiff) + 
                             ", targetHigher=" + targetHigher);
        }
        
        return targetHigher && buildCooldown <= 0;
    }
    
    @Override
    public boolean canContinueToUse() {
        return canUse();
    }
    
    @Override
    public void start() {
        if (WardenAIConfig.debugMode) {
            System.out.println("SimpleBuilding: STARTED - target is elevated");
        }
    }
    
    @Override
    public void tick() {
        if (buildCooldown > 0) {
            buildCooldown--;
            return;
        }
        
        if (target == null) {
            return;
        }
        
        // Step 1: If we placed a block, move to it first
        if (lastPlacedBlock != null && !isWardenOnBlock(lastPlacedBlock)) {
            moveToBlock(lastPlacedBlock);
            if (WardenAIConfig.debugMode) {
                System.out.println("SimpleBuilding: Moving to last placed block " + lastPlacedBlock);
            }
            return;
        }
        
        // Step 2: Find where to place the next block
        BlockPos nextBlockPos = findNextBlockPosition();
        if (nextBlockPos != null) {
            if (placeBlock(nextBlockPos)) {
                lastPlacedBlock = nextBlockPos;
                buildCooldown = 20; // 1 second cooldown
                
                // Immediately start moving to the new block
                moveToBlock(nextBlockPos);
                
                if (WardenAIConfig.debugMode) {
                    System.out.println("SimpleBuilding: Placed block at " + nextBlockPos + ", moving to it");
                }
            }
        }
    }
    
    private BlockPos findNextBlockPosition() {
        BlockPos wardenPos = warden.blockPosition();
        BlockPos targetPos = target.blockPosition();
        
        // Simple approach: place block one step towards target, one block higher
        int deltaX = Integer.compare(targetPos.getX(), wardenPos.getX());
        int deltaZ = Integer.compare(targetPos.getZ(), wardenPos.getZ());
        
        // Try positions in order of preference
        BlockPos[] candidates = {
            // Directly towards target, one block up
            wardenPos.offset(deltaX, 1, deltaZ),
            // Just up from current position
            wardenPos.offset(0, 1, 0),
            // Towards target horizontally, then up
            wardenPos.offset(deltaX, 1, 0),
            wardenPos.offset(0, 1, deltaZ)
        };
        
        for (BlockPos candidate : candidates) {
            if (isValidPlacePosition(candidate)) {
                return candidate;
            }
        }
        
        return null;
    }
    
    private boolean isValidPlacePosition(BlockPos pos) {
        // Check if we can place a block here
        BlockState currentState = warden.level().getBlockState(pos);
        BlockState belowState = warden.level().getBlockState(pos.below());
        
        // Must be air or replaceable, and have solid ground below
        return (currentState.isAir() || currentState.canBeReplaced()) && 
               !belowState.isAir() && 
               belowState.isSolid();
    }
    
    private boolean placeBlock(BlockPos pos) {
        if (!isValidPlacePosition(pos)) {
            return false;
        }
        
        // Place cobblestone block
        BlockState blockToPlace = Blocks.COBBLESTONE.defaultBlockState();
        warden.level().setBlock(pos, blockToPlace, 3);
        
        // Add to warden's placed blocks list for cleanup
        warden.getPlacedBlocks().add(pos);
        
        // Play sound
        warden.level().playSound(null, pos, blockToPlace.getSoundType().getPlaceSound(),
                               warden.getSoundSource(), 1.0F, 1.0F);
        
        return true;
    }
    
    private void moveToBlock(BlockPos blockPos) {
        // Move to the center of the block, one block above it
        double x = blockPos.getX() + 0.5;
        double y = blockPos.getY() + 1.0;
        double z = blockPos.getZ() + 0.5;
        
        warden.getNavigation().moveTo(x, y, z, 1.2); // Fast movement
    }
    
    private boolean isWardenOnBlock(BlockPos blockPos) {
        BlockPos wardenPos = warden.blockPosition();
        // Check if warden is standing on or very close to the block
        return Math.abs(wardenPos.getX() - blockPos.getX()) <= 1 && 
               Math.abs(wardenPos.getZ() - blockPos.getZ()) <= 1 && 
               Math.abs(wardenPos.getY() - (blockPos.getY() + 1)) <= 1;
    }
    
    @Override
    public void stop() {
        lastPlacedBlock = null;
        if (WardenAIConfig.debugMode) {
            System.out.println("SimpleBuilding: STOPPED");
        }
    }
}
